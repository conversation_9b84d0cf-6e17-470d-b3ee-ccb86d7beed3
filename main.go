package main

import (
	"bff-go-shopping-static-store/src/config/consul"
	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/config/server"
)

// @title bff-go-shopping-static-store
// @version 1.0
// @securityDefinitions.apikey  ApiKeyAuth
// @in                          header
// @name                        access_token
// @description					user access token
func main() {

	logger.ConfigLogger()
	consul.ReadConfig()
	server.InitServer()
}
