package gtw

import "bff-go-shopping-static-store/src/entities"

type GTWPackagesResponse struct {
	Packages []GTWPackage     `json:"packages"`
	Meta     GTWMeta          `json:"meta"`
	Metrics  entities.Metrics `json:"metrics"`
}

type GTWMeta struct {
	Hotels            []GTWMetaHotels          `json:"hotels"`
	MealPlans         []GTWMetaMealPlans       `json:"mealPlans"`
	Categories        []GTWMetaCategories      `json:"categories"`
	TotalPackageValue GTWMetaTotalPackageValue `json:"totalPackageValue"`
	TotalPages        int                      `json:"totalPages"`
	TotalPackages     int                      `json:"totalPackages"`
}
type GTWMetaHotels struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
type GTWMetaMealPlans struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}
type GTWMetaCategories struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}
type GTWMetaTotalPackageValue struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}
