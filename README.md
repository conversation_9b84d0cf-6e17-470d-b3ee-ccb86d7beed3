[![Build status](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-bff-go-shopping-static-store/badge/icon)](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-bff-go-shopping-static-store/)
[![Quality Gate](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-go-shopping-static-store&metric=alert_status)](https://sonar.app.cvc.com.br/dashboard?id=bff-go-shopping-static-store)
[![Coverage](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-go-shopping-static-store&metric=coverage)](https://sonar.app.cvc.com.br/component_measures?id=bff-go-shopping-static-store&metric=Coverage)
[![Maintainnability](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-go-shopping-static-store&metric=sqale_rating)](https://sonar.app.cvc.com.br/component_measures?id=bff-go-shopping-static-store&metric=Maintainability)
[![Security](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-go-shopping-static-store&metric=security_rating)](https://sonar.app.cvc.com.br/component_measures?id=bff-go-shopping-static-store)

# bff-go-shopping-static-store

BFF para o serviço de compras estáticas. Este projeto fornece uma interface para gerenciamento e busca de informações relacionadas a lojas estáticas no contexto de compras integradas.

---

## 🚀 Como rodar o projeto localmente

### Pré-requisitos

- **Golang** (versão recomendada: 1.17 ou superior)

### Instruções

1. Clone o repositório:
   ```bash
   <NAME_EMAIL>:Desenvolvimento-MS/bff-go-shopping-static-store.git
   cd bff-go-shopping-static-store
   ```

2. Configure as variáveis de ambiente necessárias:
   ```bash
   export GTW_ACCESS_TOKEN_DEFAULT=<token_default>
   export GTW_PACKAGES=<packages_url>
   ... # Adicione outras variáveis conforme necessário
   ```

3. Execute o projeto localmente:
   ```bash
   go run main.go
   ```

4. O projeto estará disponível localmente na porta configurada.

---

## 🌐 URLs dos Ambientes

### Ambiente de TI

- **URL Base:** [http://bff-go-shopping-static-store.k8s-ti-cvc.com.br](http://bff-go-shopping-static-store.k8s-ti-cvc.com.br)

### Ambiente de QA

- **URL Base:** [http://bff-go-shopping-static-store.k8s-qa-cvc.com.br](http://bff-go-shopping-static-store.k8s-qa-cvc.com.br)

### Ambiente de Produção (PROD)

- **URL Base:** [http://bff-go-shopping-static-store.k8s-cvc.com.br](http://bff-go-shopping-static-store.k8s-cvc.com.br)

---

## 📄 URLs de Exemplo para Testes

### Busca de Lojas Estáticas:
```bash
curl -X GET 'http://localhost:8080/v1/locations?name=shopping&size=10&isOrigin=true' \
-H 'Authorization: Bearer <TOKEN>'
```

### Busca de Pacotes:
```bash
curl -X POST 'http://localhost:8080/v1/packages' \
-H 'Authorization: Bearer <TOKEN>' \
-H 'Content-Type: application/json' \
-d '{"origin":"SAO","destination":"RIO","rooms":"30,30","startCheckInDate":"2024-01-01","endCheckInDate":"2024-01-10"}'
```

---

## 🛠️ Como fazer um deploy

1. Crie uma merge request no repositório.
2. Caso o deploy seja para QA ou PROD:
   - Crie um RFC no Jira e siga os passos necessários.
3. Após aprovação, realize o merge para a branch `master`.
4. O pipeline no Jenkins será executado automaticamente.
5. Após o deploy ser finalizado, valide se o serviço está funcionando corretamente.
6. Se necessário, faça o rollback manual para uma versão anterior.

---

## 📂 Arquitetura do Projeto

A arquitetura do projeto é baseada no modelo BFF (Backend for Frontend), utilizando o framework **Golang**. Ela foi projetada para garantir alta performance e escalabilidade ao lidar com requisições relacionadas a pacotes e lojas estáticas.

---

## 📄 Swagger

A documentação Swagger do projeto pode ser acessada em:

- **Ambiente de TI:** [http://bff-go-shopping-static-store.k8s-ti-cvc.com.br/swagger](http://bff-go-shopping-static-store.k8s-ti-cvc.com.br/swagger)
- **Ambiente de QA:** [http://bff-go-shopping-static-store.k8s-qa-cvc.com.br/swagger](http://bff-go-shopping-static-store.k8s-qa-cvc.com.br/swagger)
- **Ambiente de Produção:** [http://bff-go-shopping-static-store.k8s-cvc.com.br/swagger](http://bff-go-shopping-static-store.k8s-cvc.com.br/swagger)

---

## 🗂️ Configurações no Consul

As configurações do projeto estão organizadas no Consul para os diferentes ambientes:

- **Ambiente TI:** [http://consul-dev.services.cvc.com.br:8500/ui/consul-dev/kv/bff-go-shopping-static-store](http://consul-dev.services.cvc.com.br:8500/ui/consul-dev/kv/bff-go-shopping-static-store)
- **Ambiente QA:** [http://consul-qa.services.cvc.com.br:8500/ui/consul-qa/kv/bff-go-shopping-static-store](http://consul-qa.services.cvc.com.br:8500/ui/consul-qa/kv/bff-go-shopping-static-store)
- **Ambiente PROD:** [http://consul-prod.services.cvc.com.br:8500/ui/consul-prod/kv/bff-go-shopping-static-store](http://consul-prod.services.cvc.com.br:8500/ui/consul-prod/kv/bff-go-shopping-static-store)

---

## Query para logs no Kibana
https://kibana.services.cvc.com.br/app/kibana#/discover?_g=()&_a=(columns:!(message),index:'5725b180-ba9d-11e8-be0f-396272e87c50',interval:auto,query:(language:kuery,query:'kubernetes.namespace_name:%20%22bff-go-shopping-static-store%22%20'),sort:!('@timestamp',desc))

## {MATRIZ DE RESILIÊNCIA}
{COLOCAR O DESENHO DA MATRIZ DE RESILIÊNCIA (Ex essa abaixo)}
http://git.cvc.com.br/shopping/package/bff-go-shopping-static-store/README.md

## 🛡️ Logs e Monitoramento

- Os logs do projeto estão configurados para serem enviados ao **Logstash** via **GELF**.
- Visualize logs em:
  - **Kibana TI:** [https://kibana.services.cvc.com.br/app/kibana#/discover?_g=()&_a=(query:(language:kuery,query:'kubernetes.namespace_name:%20%22bff-go-shopping-static-store%22'))](https://kibana.services.cvc.com.br/app/kibana#/discover?_g=()&_a=(query:(language:kuery,query:'kubernetes.namespace_name:%20%22bff-go-shopping-static-store%22')))

---

## 🔧 Serviços Externos

### Integração com Catálogo de Produtos
- Integração para obter dados de produtos disponíveis no catálogo central.
- **URL Base QA:** [https://api.catalogo.qa.services.cvc.com.br/products](https://api.catalogo.qa.services.cvc.com.br/products)

Exemplo de requisição:
```bash
curl -X GET 'https://api.catalogo.qa.services.cvc.com.br/products?category=electronics&availability=true' \
-H 'Authorization: Bearer <TOKEN>'
```

---

## 👥 Equipe

**Go Shopping Static Store Team**  
Veja a lista de [colaboradores](https://git.cvc.com.br/Desenvolvimento-MS/bff-go-shopping-static-store/-/graphs/master) que participaram deste projeto.

---
