package gtw

import "bff-go-shopping-static-store/src/entities"

type GTWPromotion struct {
	AvailableItems []PromotionItem `json:"AvailableItems"`
	SelectedItems  []PromotionItem `json:"SelectedItems"`
	Metrics        []entities.MetricsResult
}

type PromotionItem struct {
	KeyRateToken string     `json:"keyRateToken"`
	Rph          int        `json:"rph"`
	RateToken    string     `json:"rateToken"`
	HasCombo     bool       `json:"hasCombo"`
	Promotion    *Promotion `json:"promotion"`
}

type Promotion struct {
	Statements            []Statement `json:"statements"`
	PriceWithTax          float64     `json:"priceWithTax"`
	PriceWithoutTax       float64     `json:"priceWithoutTax"`
	PricePerDayWithTax    float64     `json:"pricePerDayWithTax"`
	PricePerDayWithoutTax float64     `json:"pricePerDayWithoutTax"`
	Percentage            float64     `json:"percentage"`
	DiscountApplied       string      `json:"discountApplied"`
}

type Statement struct {
	Code     string  `json:"code"`
	Name     string  `json:"name"`
	Label    string  `json:"label"`
	Type     string  `json:"type"`
	Discount float64 `json:"discount"`
}
