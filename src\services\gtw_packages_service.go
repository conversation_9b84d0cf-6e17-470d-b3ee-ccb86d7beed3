package services

import (
	"bff-go-shopping-static-store/src/config"
	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/entities"
	"bff-go-shopping-static-store/src/entities/gtw"
	"bff-go-shopping-static-store/src/util"
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/httputil"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

const SEARCH_PRODUCT_TYPE = "AIR"

type GTWPackagesService struct {
	config *config.BFFConfig
}

func NewGTWPackagesService() *GTWPackagesService {
	return &GTWPackagesService{
		config: config.GetConfig(),
	}
}

func (r *GTWPackagesService) GetLocations(header entities.Header, isOrigin bool, isDestination bool, originId string, destinationId string, size int, productType *string, includeAirProtection bool, ctx context.Context) ([]gtw.GTWLocation, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetLocations"

	uri := r.config.GTWPackages + r.config.GTWPackagesLocations

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri)

	var response gtw.GTWLocationWrapper
	var restError gtw.GTWRestError

	// request = request.SetQueryParams(map[string]string{
	// 	"active":  strconv.FormatBool(true),
	// 	"charter": strconv.FormatBool(true),
	// 	//"charterTypes":       "AIR,COMBO",
	// 	"originCharter":      strconv.FormatBool(isOrigin),
	// 	"destinationCharter": strconv.FormatBool(isDestination),
	// 	"size":               strconv.Itoa(size),
	// })
	// if originId != "" {
	// 	request = request.SetQueryParam("originId", originId)
	// }
	// if destinationId != "" {
	// 	request = request.SetQueryParam("destinationId", destinationId)
	// }
	qString := []string{
		"active=" + strconv.FormatBool(true),
		"charter=" + strconv.FormatBool(true),
		"originCharter=" + strconv.FormatBool(isOrigin),
		"destinationCharter=" + strconv.FormatBool(isDestination),
		"size=" + strconv.Itoa(size),
	}
	if originId != "" {
		qString = append(qString, "originId="+originId)
	}
	if destinationId != "" {
		qString = append(qString, "destinationId="+destinationId)
	}

	types := strings.Split(r.config.GTWPackagesProductType, ",")

	if productType != nil {

		types = strings.Split(string(*productType), ",")
	}

	for i := 0; i < len(types); i++ {
		qString = append(qString, "charterTypes="+types[i])
	}

	for i := 0; i < len(types); i++ {
		qString = append(qString, "productType="+types[i])
	}

	ip := strconv.FormatBool(includeAirProtection)
	qString = append(qString, "includeAirProtection="+ip)

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri+" PARAMS: "+strings.Join(qString, "&"))

	httpResponse, err := r.getRestClient(header, ctx).
		SetQueryString(strings.Join(qString, "&")).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return nil, errors.New("error executing api")
	}
	if httpResponse.StatusCode() == 404 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		return []gtw.GTWLocation{}, nil
	}
	if httpResponse.StatusCode() != 200 {
		if (gtw.GTWRestError{} != restError) {
			logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
			return nil, errors.New(restError.Message)
		} else {
			logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
			return nil, errors.New("api returns status: " + httpResponse.Status())
		}
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response.Content, nil
}

func (r *GTWPackagesService) GetLocationsPackage(header entities.Header, active bool, iata string, charter bool, zoneId string, ctx context.Context) ([]gtw.GTWLocation, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetLocationsPackage"

	uri := r.config.GTWPackages + r.config.GTWPackagesLocations

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri)

	var response gtw.GTWLocationWrapper
	var restError gtw.GTWRestError

	qString := []string{
		"active=" + strconv.FormatBool(active),
		"charter=" + strconv.FormatBool(charter),
	}

	if iata != "" {
		qString = append(qString, "iata="+iata)
	}

	if zoneId != "" {
		qString = append(qString, "zoneId="+zoneId)
	}

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri+" PARAMS: "+strings.Join(qString, "&"))

	httpResponse, err := r.getRestClient(header, ctx).
		SetQueryString(strings.Join(qString, "&")).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return nil, errors.New("error executing api")
	}
	if httpResponse.StatusCode() == 404 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		return []gtw.GTWLocation{}, nil
	}
	if httpResponse.StatusCode() != 200 {
		if (gtw.GTWRestError{} != restError) {
			logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
			return nil, errors.New(restError.Message)
		} else {
			logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
			return nil, errors.New("api returns status: " + httpResponse.Status())
		}
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response.Content, nil
}

func (r *GTWPackagesService) GetRelatedLocations(header entities.Header, originId string, destinationId string, ctx context.Context) ([]gtw.GTWLocation, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetRelatedLocations"

	uri := r.config.GTWPackages + r.config.GTWPackagesLocations + "/" + originId + "/destinations-charter"
	if destinationId != "" {
		uri = r.config.GTWPackages + r.config.GTWPackagesLocations + "/" + destinationId + "/origins-charter"
	}
	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri)

	var response []gtw.GTWLocation
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return nil, errors.New("error executing api")
	}
	if httpResponse.StatusCode() == 404 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		return []gtw.GTWLocation{}, nil
	}
	if httpResponse.StatusCode() != 200 {
		if (gtw.GTWRestError{} != restError) {
			logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
			return nil, errors.New(restError.Message)
		} else {
			logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
			return nil, errors.New("api returns status: " + httpResponse.Status())
		}
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")

	return response, nil
}

func (r *GTWPackagesService) GetPackages(header entities.Header, origin string, destination string, fromIata string, toIata string, startCheckInDate string, endCheckInDate string, rooms string, ctx context.Context, onlyBestPrice *bool, includeAirProtection *bool, productType *string) (gtw.GTWPackagesResponse, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetPackages"

	uri := r.config.GTWPackages + r.config.GTWPackagesSearch
	// queryParams := map[string]string{
	// 	"productType":      r.config.GTWPackagesProductType,
	// 	"origin":           origin,
	// 	"destination":      destination,
	// 	"startCheckInDate": startCheckInDate,
	// 	"endCheckInDate":   endCheckInDate,
	// 	"rooms":            rooms,
	// }

	qString := []string{
		"startCheckInDate=" + startCheckInDate,
		"endCheckInDate=" + endCheckInDate,
		"rooms=" + rooms,
	}

	if origin != "" {
		qString = append(qString, "origin="+origin)
	}
	if destination != "" {
		qString = append(qString, "destination="+destination)
	}
	if fromIata != "" {
		qString = append(qString, "fromIata="+fromIata)
	}
	if toIata != "" {
		qString = append(qString, "toIata="+toIata)
	}

	types := strings.Split(r.config.GTWPackagesProductType, ",")

	if productType != nil {

		types = strings.Split(string(*productType), ",")
	}

	for i := 0; i < len(types); i++ {
		qString = append(qString, "productType="+types[i])
	}

	if includeAirProtection != nil {
		ip := strconv.FormatBool(*includeAirProtection)
		qString = append(qString, "includeAirProtection="+ip)
	}

	if onlyBestPrice != nil {

		obp := strconv.FormatBool(*onlyBestPrice)
		qString = append(qString, "onlyBestPrice="+obp)
	}

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri+" PARAMS: "+strings.Join(qString, "&"))
	var response gtw.GTWPackagesResponse
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetQueryString(strings.Join(qString, "&")).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, errors.New("error executing api")
	}
	if httpResponse.StatusCode() != 200 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		if httpResponse.StatusCode() == 404 {
			return response, nil
		}
		if (gtw.GTWRestError{} != restError) {
			logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
			return response, errors.New(restError.Message)
		}
		return response, errors.New("api returns status: " + httpResponse.Status())
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response, nil
}

func (r *GTWPackagesService) GetPackagesByLocation(header entities.Header, origin string, destination string, startCheckInDate string, endCheckInDate string, rooms string, productType *string, ctx context.Context) (gtw.GTWPackagesResponse, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetPackagesByLocation"
	uri := r.config.GTWPackages + r.config.GTWPackagesSearch

	qString := []string{
		"startCheckInDate=" + startCheckInDate,
		"endCheckInDate=" + endCheckInDate,
		"rooms=" + rooms,
	}
	if origin != "" {
		uri += "/search-by-origin"
		qString = append(qString, "location="+origin)
	}
	if destination != "" {
		uri += "/search-by-destination"
		qString = append(qString, "location="+destination)
	}

	types := strings.Split(r.config.GTWPackagesProductType, ",")
	if productType != nil {
		types = strings.Split(string(*productType), ",")
	}
	for i := 0; i < len(types); i++ {
		qString = append(qString, "productType="+types[i])
	}

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri+" PARAMS: "+strings.Join(qString, "&"))
	var response gtw.GTWPackagesResponse
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetQueryString(strings.Join(qString, "&")).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, errors.New("error executing api")
	}
	if httpResponse.StatusCode() != 200 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		if httpResponse.StatusCode() == 404 {
			return response, nil
		}
		if (gtw.GTWRestError{} != restError) {
			logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
			return response, errors.New(restError.Message)
		}
		return response, errors.New("api returns status: " + httpResponse.Status())
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response, nil
}

func (r *GTWPackagesService) GetPackageByToken(header entities.Header, packageToken string, ctx context.Context) (gtw.GTWPackage, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetPackages"

	uri := r.config.GTWPackages + r.config.GTWPackagesSearch + "/" + packageToken
	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri)

	var response gtw.GTWPackage
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, errors.New("error executing api")
	}
	if (gtw.GTWRestError{} != restError) {
		logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
		return response, errors.New(restError.Message)
	}
	if httpResponse.StatusCode() != 200 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		return response, errors.New("api returns status: " + httpResponse.Status())
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")

	return response, nil
}

func (r *GTWPackagesService) GetDCR(header entities.Header, packageToken string, ctx context.Context) (gtw.GTWDcr, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetDCR"

	uri := r.config.GTWPackages + r.config.GTWPackagesSearch + "/" + packageToken + "/dcr"
	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri)

	var response gtw.GTWDcr
	var restError gtw.GTWRestError
	httpResponse, err := r.getRestClient(header, ctx).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, errors.New("error executing api")
	}
	if (gtw.GTWRestError{} != restError) {
		logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
		return response, errors.New(restError.Message)
	}
	if httpResponse.StatusCode() != 200 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		return response, errors.New("api returns status: " + httpResponse.Status())
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")

	return response, nil
}

func (r *GTWPackagesService) GetCalendar(header entities.Header, origin string, destination string, startCheckInDate string, numberOfMonths int, rooms string, productType *string, includeAirProtection bool, ctx context.Context) (gtw.GTWCalendarWrapper, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetCalendar"

	uri := r.config.GTWPackages + r.config.GTWPackagesCalendar
	// queryParams := map[string]string{
	// 	"productType":      r.config.GTWPackagesProductType,
	// 	"origin":           origin,
	// 	"destination":      destination,
	// 	"startCheckInDate": startCheckInDate,
	// 	"numberOfMonths":   strconv.Itoa(numberOfMonths),
	// 	"rooms":            rooms,
	// }
	qString := []string{
		"origin=" + origin,
		"destination=" + destination,
		"rooms=" + rooms,
	}

	if numberOfMonths > 0 {
		qString = append(qString, "numberOfMonths="+strconv.Itoa(numberOfMonths))
	}

	if startCheckInDate != "" {
		qString = append(qString, "startCheckInDate="+startCheckInDate)
	}

	types := strings.Split(r.config.GTWPackagesProductType, ",")

	if productType != nil {

		types = strings.Split(string(*productType), ",")
	}

	for i := 0; i < len(types); i++ {
		qString = append(qString, "productType="+types[i])
	}

	ip := strconv.FormatBool(includeAirProtection)
	qString = append(qString, "includeAirProtection="+ip)

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri+" PARAMS: "+strings.Join(qString, "&"))

	var response gtw.GTWCalendarWrapper
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetQueryString(strings.Join(qString, "&")).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, errors.New("error executing api")
	}
	if httpResponse.StatusCode() != 200 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		if httpResponse.StatusCode() == 404 {
			return response, nil
		}
		if (gtw.GTWRestError{} != restError) {
			logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
			return response, errors.New(restError.Message)
		}
		return response, errors.New("api returns status: " + httpResponse.Status())
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")

	return response, nil
}

func (r *GTWPackagesService) GetAlternativePackages(header entities.Header, origin string, destination string, startCheckInDate string, endCheckInDate string, rooms string, productType *string, includeAirProtection bool, ctx context.Context) (gtw.GTWPackagesResponse, error) {
	startTime := time.Now()
	source := "GTWPackagesService.GetAlternativePackages"

	uri := r.config.GTWPackages + r.config.GTWPackagesSearch + "/alternative-avails"
	// queryParams := map[string]string{
	// 	"productType":      r.config.GTWPackagesProductType,
	// 	"origin":           origin,
	// 	"destination":      destination,
	// 	"startCheckInDate": startCheckInDate,
	// 	"endCheckInDate":   endCheckInDate,
	// 	"rooms":            rooms,
	// }
	qString := []string{
		"origin=" + origin,
		"destination=" + destination,
		"startCheckInDate=" + startCheckInDate,
		"endCheckInDate=" + endCheckInDate,
		"rooms=" + rooms,
	}

	ip := strconv.FormatBool(includeAirProtection)
	qString = append(qString, "includeAirProtection="+ip)

	types := strings.Split(r.config.GTWPackagesProductType, ",")

	if productType != nil {

		types = strings.Split(string(*productType), ",")
	}

	for i := 0; i < len(types); i++ {
		qString = append(qString, "productType="+types[i])
	}
	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri+" PARAMS: "+strings.Join(qString, "&"))

	var response gtw.GTWPackagesResponse
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetQueryString(strings.Join(qString, "&")).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, errors.New("error executing api")
	}
	if (gtw.GTWRestError{} != restError) {
		logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
		return response, errors.New(restError.Message)
	}
	if httpResponse.StatusCode() != 200 {
		logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
		return response, errors.New("api returns status: " + httpResponse.Status())
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response, nil
}

func (r *GTWPackagesService) GetAdvancedSearch(header entities.Header, paxes string, departureDate string, flightsGroupUserId string, reservationCode string, tourCode string, packageId string, ctx context.Context) (gtw.GTWPackagesResponse, *gtw.GTWRestError) {
	startTime := time.Now()
	source := "GTWPackagesService.GetAdvancedSearch"

	uri := r.config.GTWPackages + r.config.GTWPackagesSearch + "/advanced-search"
	var queryParams = map[string]string{}
	// queryParams["paxes"] = paxes
	queryParams["paxes"] = strings.Replace(paxes, "+", " ", -1)
	queryParams["departureDate"] = departureDate

	if flightsGroupUserId != "" {
		queryParams["flightsGroupUserId"] = flightsGroupUserId
	}
	if reservationCode != "" {
		queryParams["reservationCode"] = reservationCode
	}
	if tourCode != "" {
		queryParams["tourCode"] = tourCode
	}
	if packageId != "" {
		queryParams["packageId"] = packageId
	}

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri+" PARAMS: "+fmt.Sprintln(queryParams))

	var response gtw.GTWPackagesResponse
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetQueryParams(queryParams).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, gtw.NewGTWError(httpResponse.StatusCode(), restError.Message)
	}

	if httpResponse.StatusCode() != 200 {
		if httpResponse.StatusCode() == http.StatusNotFound {
			logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
			return response, gtw.NewNotFoundError(restError.Message)
		}
		logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
		return response, gtw.NewGTWError(httpResponse.StatusCode(), restError.Message)
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response, nil
}

func (*GTWPackagesService) printCurlCommand(httpResponse *resty.Response) {
	req := httpResponse.Request.RawRequest

	curlCommand, err := httputil.DumpRequestOut(req, true)
	if err != nil {
		fmt.Println("Erro ao criar comando cURL:", err)
		return
	}
	_ = len(curlCommand)

	// Descomentar para printar o cURL
	fmt.Println("CURL ------------------------------------------------------------------------------------")
	fmt.Println(string(curlCommand))
	fmt.Println("CURL FINAL --- Convert http to cUrl [https://curl.se/h2c/] -------------------------------")
}

func (r *GTWPackagesService) HasAvailByRateToken(header entities.Header, rateToken string, ctx context.Context) (gtw.GTWPackageHasAvail, *gtw.GTWRestError) {
	startTime := time.Now()
	source := "GTWPackagesService.HasAvailByRateToken"

	uri := r.config.GTWPackages + r.config.GTWPackagesHasAvail + "/" + rateToken
	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri)

	var response gtw.GTWPackageHasAvail
	var restError gtw.GTWRestError

	httpResponse, err := r.getRestClient(header, ctx).
		SetResult(&response).
		SetError(&restError).
		Get(uri)

	r.printCurlCommand(httpResponse)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api")
		return response, gtw.NewGTWError(httpResponse.StatusCode(), restError.Message)
	}

	if httpResponse.StatusCode() != 200 {
		if httpResponse.StatusCode() == http.StatusNotFound {
			logger.InfoWithContext(ctx, source, "error", "api returns status: "+httpResponse.Status())
			return response, gtw.NewNotFoundError(restError.Message)
		}
		logger.ErrorWithContext(ctx, err, source, "error", restError.Message)
		return response, gtw.NewGTWError(httpResponse.StatusCode(), restError.Message)
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")

	return response, nil
}

func (r *GTWPackagesService) getRestClient(header entities.Header, ctx context.Context) *resty.Request {
	client := resty.New()

	request := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Content-Encoding", "gzip, br").
		SetHeader("gtw-transaction-id", header.TransactionId)
		
	// Adicionando o header do Profit Split apenas se tiver valor
	if header.GtwPricing != "" {
		request = request.SetHeader("gtw-pricing", header.GtwPricing)
	}

	accessToken := header.AccessToken
	if accessToken == "" {
		accessToken = r.config.GTWAccessTokenDefault
	}

	credential, err := util.ParseAccessToken(accessToken)
	if err != nil {
		logger.ErrorWithContext(ctx, err, "GTWPackagesService.getRestClient", "error", err.Error())
	} else {
		request = request.
			SetHeader("access_token", accessToken).
			SetHeader("gtw-agent-sign", credential.AgentSign).
			SetHeader("gtw-username", credential.User).
			SetHeader("gtw-branch-id", strconv.Itoa(credential.BranchID))
	}

	return request
}
