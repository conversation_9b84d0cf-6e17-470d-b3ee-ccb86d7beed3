// "github.com/fabienm/go-logrus-formatters"
package formatters

import (
	"encoding/json"
	"time"

	"github.com/sirupsen/logrus"
)

const (
	// GelfVersion is the supported gelf version
	GelfVersion = "1.1"
)

var (
	levelMap        map[logrus.Level]int
	syslogNameMap   map[logrus.Level]string
	protectedFields map[string]bool
)

func init() {
	levelMap = map[logrus.Level]int{
		logrus.FatalLevel: 2,
		logrus.ErrorLevel: 3,
		logrus.WarnLevel:  4,
		logrus.InfoLevel:  6,
		logrus.DebugLevel: 7,
	}
	syslogNameMap = map[logrus.Level]string{
		logrus.FatalLevel: "fatal",
		logrus.ErrorLevel: "error",
		logrus.WarnLevel:  "warn",
		logrus.InfoLevel:  "info",
		logrus.DebugLevel: "debug",
	}
	protectedFields = map[string]bool{
		"short_message": true,
	}
}

type gelfFormatter struct {
	hostname string
}

// New<PERSON><PERSON> returns a new logrus / gelf-compliant formatter
func NewGelf(hostname string) gelfFormatter {
	return gelfFormatter{hostname: hostname}
}

// Format implements logrus formatter
func (f gelfFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	gelfEntry := map[string]interface{}{
		"type":        "gelf",
		"@version":    "1",
		"version":     GelfVersion,
		"host":        f.hostname,
		"level":       toSyslogLevel(entry.Level),
		"_level_name": toSyslogLevelName(entry.Level),
		"@timestamp":  toTimestamp(entry.Time),
		"timestamp":   toTimestamp(entry.Time),
		"message":     entry.Message,
	}
	for key, value := range entry.Data {
		if protectedFields[key] {
			key = "_" + key
		}
		gelfEntry[key] = value
	}
	message, err := json.Marshal(gelfEntry)
	return message, err
}

func toTimestamp(t time.Time) string {
	return t.Format("2006-01-02T15:04:05.000-07:00")
}

func toSyslogLevel(level logrus.Level) int {
	syslog, ok := levelMap[level]
	if ok {
		return syslog
	}
	return 0
}

func toSyslogLevelName(level logrus.Level) string {
	syslog, ok := syslogNameMap[level]
	if ok {
		return syslog
	}
	return ""
}
