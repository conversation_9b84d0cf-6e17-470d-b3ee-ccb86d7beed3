package gtw

type GTWHotel struct {
	ID             string         `json:"id"`
	Risk           RiskLevel      `json:"risk"`
	Name           string         `json:"name"`
	Description    string         `json:"description"`
	Award          int            `json:"award"`
	Rooms          []GTWRooms     `json:"rooms"`
	Links          GTWLinks       `json:"links"`
	Amenities      []GTWAmenities `json:"amenities"`
	Priority       int            `json:"priority"`
	IsPlus         bool           `json:"isPlus"`
	IsPreferential bool           `json:"isPreferential"`
	Location       GTWLocations   `json:"location"`
	CheckIn        string         `json:"checkIn"`
	CheckOut       string         `json:"checkOut"`
	NumberOfNights int            `json:"numberOfNights"`
}

type GTWHotelTaxes struct {
	Description string  `json:"description"`
	Percent     float64 `json:"percent"`
	Amount      float64 `json:"amount"`
}

type GTWRates struct {
	PackageGroup          string          `json:"packageGroup"`
	RateToken             string          `json:"rateToken"`
	Currency              string          `json:"currency"`
	PriceWithTax          float64         `json:"priceWithTax"`
	PriceWithoutTax       float64         `json:"priceWithoutTax"`
	PricePerDayWithTax    float64         `json:"pricePerDayWithTax"`
	PricePerDayWithoutTax float64         `json:"pricePerDayWithoutTax"`
	PricePerPaxWithTax    float64         `json:"pricePerPaxWithTax"`
	PricePerPaxWithoutTax float64         `json:"pricePerPaxWithoutTax"`
	Profit                float64         `json:"profit"`
	Taxes                 []GTWHotelTaxes `json:"taxes"`
	Promotion             *PromotionItem  `json:"promotionItem,omitempty"`
}

type GTWRooms struct {
	Rph      int        `json:"rph"`
	Name     string     `json:"name"`
	MealPlan string     `json:"mealPlan"`
	Category string     `json:"category"`
	Rates    []GTWRates `json:"rates"`
}

type GTWAmenities struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

type GTWLocations struct {
	Address     string         `json:"address"`
	Coordinates GTWCoordinates `json:"coordinates"`
}

// return room value
func (r *GTWRooms) GetRoomValue() float64 {
	// Verifica se há um objeto Promotion associado
	if r.Rates[0].Promotion != nil && r.Rates[0].Promotion.Promotion != nil {
		// Verifica se o campo PriceWithTax dentro do objeto Promotion é válido
		if r.Rates[0].Promotion.Promotion.PriceWithTax > 0 {
			return r.Rates[0].Promotion.Promotion.PriceWithTax
		}
	}

	// Se não houver Promotion ou PriceWithTax inválido, retorna o valor padrão
	return r.Rates[0].PriceWithTax
}

// SortRoomByLowerPrice implements sort.Interface based on the PriceWithTax
type SortRoomByLowerPrice []GTWRooms

func (a SortRoomByLowerPrice) Len() int { return len(a) }
func (a SortRoomByLowerPrice) Less(i, j int) bool {
	return a[i].GetRoomValue() < a[j].GetRoomValue()
}
func (a SortRoomByLowerPrice) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

// SortRoomByHigherPrice implements sort.Interface based on the PriceWithTax
type SortRoomByHigherPrice []GTWRooms

func (a SortRoomByHigherPrice) Len() int { return len(a) }
func (a SortRoomByHigherPrice) Less(i, j int) bool {
	return a[i].GetRoomValue() > a[j].GetRoomValue()
}
func (a SortRoomByHigherPrice) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
