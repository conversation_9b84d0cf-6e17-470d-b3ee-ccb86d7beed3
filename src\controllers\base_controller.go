package controllers

import (
	"bff-go-shopping-static-store/src/entities"
	"context"

	"github.com/labstack/echo/v4"
)

func GetAccessToken(context echo.Context) string {
	return context.Request().Header.Get("access_token")
}

func GetHeader(ctx echo.Context) entities.Header {
	header := entities.Header{}
	header.AccessToken = ctx.Request().Header.Get("access_token")
	header.TransactionId = ctx.Request().Header.Get("transactionid")
	header.GtwPricing = ctx.Request().Header.Get("gtw-pricing")
	return header
}

func GetLoggerContext(ctx echo.Context) context.Context {
	return ctx.Get("logCtx").(context.Context)
}
