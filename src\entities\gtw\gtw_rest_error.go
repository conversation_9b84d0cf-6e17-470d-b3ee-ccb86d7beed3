package gtw

import "fmt"

type GTWRestError struct {
	Code       int    `json:"code,omitempty"`
	StatusCode int    `json:"statusCode,omitempty"`
	Message    string `json:"message"`
	Details    string `json:"details,omitempty"`
	Stack      string `json:"stack,omitempty"`
}

func NewGTWError(statusCode int, details string) *GTWRestError {

	return &GTWRestError{
		StatusCode: statusCode,
		Message:    "Error on executing api",
		Details:    fmt.Sprintf("GTW return: %s", details),
	}

}

func NewNotFoundError(details string) *GTWRestError {

	return &GTWRestError{
		StatusCode: 404,
		Message:    "Resource not found",
		Details:    fmt.Sprintf("GTW return: %s", details),
	}

}
