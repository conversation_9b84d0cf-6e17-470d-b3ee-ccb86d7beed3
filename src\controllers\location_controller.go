package controllers

import (
	"net/http"
	"strconv"
	"time"

	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/services"
	"bff-go-shopping-static-store/src/util"

	"github.com/labstack/echo/v4"
)

type LocationController struct {
	appService services.APPLocationsService
}

func NewLocationController(_router *echo.Echo, _appService *services.APPLocationsService) {
	handler := &LocationController{
		appService: *_appService,
	}

	_router.GET("/v1/locations", handler.getLocations)
	_router.GET("/v1/locations/package", handler.getLocationsPackage)

}

// Get all Locations godoc
// @Summary Get all locations
// @Description all locations
// @Tags Location
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} []gtw.GTWLocation
// @Param name query string false "filter by name"
// @Param size query int false "records per page (default 100)"
// @Param isOrigin query boolean false "search origins only"
// @Param origin query string false "Id of origin"
// @Param isDestination query boolean false "search destinations only"
// @Param destination query string false "Id of destination "
// @Router /v1/locations [get]
func (handler *LocationController) getLocations(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "LocationController.getLocations"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getLocations")

	header := GetHeader(ctx)
	name := ctx.QueryParam("name")
	size := 1000
	if ctx.QueryParam("size") != "" {
		size, _ = strconv.Atoi(ctx.QueryParam("size"))
	}
	originId := ctx.QueryParam("origin")
	destinationId := ctx.QueryParam("destination")
	isOrigin, _ := strconv.ParseBool(ctx.QueryParam("isOrigin"))
	isDestination, _ := strconv.ParseBool(ctx.QueryParam("isDestination"))

	var productType *string = nil

	if ctx.QueryParam("productType") != "" {
		pt := ctx.QueryParam("productType")
		productType = &pt
	}

	var includeAirProtection bool = true

	if ctx.QueryParam("includeAirProtection") == "false" {
		iap := false
		includeAirProtection = iap
	}

	resp, err := handler.appService.SearchLocations(header, name, size, isOrigin, isDestination, originId, destinationId, productType, includeAirProtection, logCtx)

	if err != nil {
		restError := util.NewGenericError("Error searching locations")
		return ctx.JSON(restError.StatusCode, restError)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}

func (handler *LocationController) getLocationsPackage(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "LocationController.getLocationsPackage"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getLocationsPackage")

	header := GetHeader(ctx)

	active, _ := strconv.ParseBool(ctx.QueryParam("active"))
	charter, _ := strconv.ParseBool(ctx.QueryParam("charter"))
	iata := ctx.QueryParam("iata")
	zoneId := ctx.QueryParam("zoneId")

	resp, err := handler.appService.SearchLocationsPackage(header, active, iata, charter, zoneId, logCtx)

	if err != nil {
		restError := util.NewGenericError("Error searching locations")
		return ctx.JSON(restError.StatusCode, restError)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}
