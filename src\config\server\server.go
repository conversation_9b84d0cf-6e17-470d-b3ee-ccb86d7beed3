package server

import (
	_ "bff-go-shopping-static-store/docs"
	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/controllers"
	"bff-go-shopping-static-store/src/services"
	"bff-go-shopping-static-store/src/util"
	"context"
	"net/http"
	"strings"
	"time"

	// instana "github.com/instana/go-sensor"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	echoSwagger "github.com/swaggo/echo-swagger"
)

func InitServer() {
	logger.Info("Starting ConfigServer:")

	//services
	gtwPackagesService := services.NewGTWPackagesService()
	gtwPromotionService := services.NewGTWPromotionService()
	appPackagesService := services.NewAPPPackagesService(gtwPackagesService, gtwPromotionService)
	appLocationsService := services.NewAPPLocationsService(gtwPackagesService)

	//config instana and server
	// sensor := instana.NewSensor("bff-go-shopping-static-store")
	// instana.SetLogger(logger.GetLogger())

	server := echo.New()
	// server.Use(InstanaMiddleware(sensor))
	server.Use(middleware.CORS())
	server.Use(middleware.Gzip())
	server.Use(middleware.RequestIDWithConfig(middleware.RequestIDConfig{
		Skipper: middlewareSkipper,
		RequestIDHandler: func(context echo.Context, id string) {
			context.Set("request_id", id)
			transactionId := context.Request().Header.Get("transactionid")
			if transactionId == "" {
				transactionId = util.NewUUID()
				context.Request().Header.Add("transactionid", transactionId)
			}
			context.Set("transaction_id", transactionId)
		},
	}))
	server.Use(middlewareContextLogger)
	server.Use(middleware.TimeoutWithConfig(middleware.TimeoutConfig{
		Skipper: middlewareSkipper,
		OnTimeoutRouteErrorHandler: func(err error, c echo.Context) {
			ctx := c.Get("logCtx").(context.Context)
			logger.ErrorWithContext(ctx, err, "server.timeout.middleware", "error", "error executing api "+c.Request().RequestURI)
		},
		Timeout: 5 * time.Minute,
	}))
	// server.Use(middlewareValidateAuthHeader)

	//swagger
	server.GET("/swagger/*", echoSwagger.WrapHandler)
	//controllers
	controllers.NewHealthcheckController(server)
	controllers.NewLocationController(server, appLocationsService)
	controllers.NewPackagesController(server, appPackagesService)

	logger.Info("APP Starting server...")
	if err := server.Start(":8080"); err != nil {
		logger.Fatal("server has refused to start: ", err)
	}
}

func middlewareSkipper(c echo.Context) bool {
	return !strings.Contains(c.Path(), "/v1/")
}

func middlewareValidateAuthHeader(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		if middlewareSkipper(c) {
			return next(c)
		} else {
			header := c.Request().Header.Get("access_token")
			if header == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, "")
			} else {
				return next(c)
			}
		}
	}
}

func middlewareContextLogger(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		if middlewareSkipper(c) {
			return next(c)
		} else {
			log := logger.ConfigContextLogger(c)
			logCtx := context.WithValue(c.Request().Context(), "logger", log)
			c.Set("logCtx", logCtx)

			requestMethod := c.Request().Method
			logger.InfoWithContext(logCtx, "server.contextLogger", "request url", requestMethod+": "+c.Request().RequestURI)
			acToken := c.Request().Header.Get("access_token")
			if acToken != "" {
				logger.InfoWithContext(logCtx, "server.contextLogger", "request access_token", acToken)
			}

			return next(c)
		}
	}
}
