### Go ###
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
shopping-cart-recomendations
/app/app

# Enviroment files
*.envDebug

# Debug files

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

### Go Patch ###
/vendor/
/Godeps/

# IDEs and editors
/.idea
.vscode/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace