apiVersion: apps/v1
kind: Deployment
metadata:
  name: bff-go-shopping-static-store-deploy
  namespace: sub-fretamento
  labels:
    app: bff-go-shopping-static-store
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bff-go-shopping-static-store
  template:
    metadata:
      labels:
        app: bff-go-shopping-static-store
    spec:
      containers:
      - name: bff-go-shopping-static-store
        image: 260584439167.dkr.ecr.sa-east-1.amazonaws.com/bff-go-shopping-static-store:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "80m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 35
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 30
        env:
          - name: INSTANA_AGENT_HOST
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
        envFrom:
          - configMapRef:
              name: bff-go-shopping-static-store
        ports:
        - containerPort: 8080
        - containerPort: 5005
