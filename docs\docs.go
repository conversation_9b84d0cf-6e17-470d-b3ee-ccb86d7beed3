// Package docs GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag
package docs

import (
	"bytes"
	"encoding/json"
	"strings"
	"text/template"

	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/v1/alternative-packages": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Search alternative packages",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Packages"
                ],
                "summary": "Search alternative packages",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id origin",
                        "name": "origin",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "id destination",
                        "name": "destination",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "check-in start date (ex: 2022-01-01)",
                        "name": "startCheckIn",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "check-in end date (ex: 2022-01-31)",
                        "name": "endCheckIn",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "rooms (ex: 30,30)",
                        "name": "rooms",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/gtw.GTWPackage"
                            }
                        }
                    }
                }
            }
        },
        "/v1/locations": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "all locations",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Location"
                ],
                "summary": "Get all locations",
                "parameters": [
                    {
                        "type": "string",
                        "description": "filter by name",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "records per page (default 100)",
                        "name": "size",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "search origins only",
                        "name": "isOrigin",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Id of origin",
                        "name": "origin",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "search destinations only",
                        "name": "isDestination",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Id of destination ",
                        "name": "destination",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/gtw.GTWLocation"
                            }
                        }
                    }
                }
            }
        },
        "/v1/packages": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Search packages",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Packages"
                ],
                "summary": "Search packages",
                "parameters": [
                    {
                        "description": "Search body request",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.RequestSearchPackages"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/gtw.GTWPackagesResponse"
                        }
                    }
                }
            }
        },
        "/v1/packages/advanced-search": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Search alternative packages",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Packages"
                ],
                "summary": "Search alternative packages",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id flightsGroupUserId",
                        "name": "flightsGroupUserId",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "id reservationCode",
                        "name": "reservationCode",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "id tourCode",
                        "name": "tourCode",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "departure date (ex: 2022-01-01)",
                        "name": "departureDate",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/gtw.GTWPackage"
                            }
                        }
                    }
                }
            }
        },
        "/v1/packages/calendar": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Get calendar",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Packages"
                ],
                "summary": "Get calendar",
                "parameters": [
                    {
                        "type": "string",
                        "description": "id origin",
                        "name": "origin",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "id destination",
                        "name": "destination",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "check-in start date (YYYY-MM-DD)",
                        "name": "startCheckIn",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "qtd of months",
                        "name": "numberOfMonths",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "rooms (ex: 30,30)",
                        "name": "rooms",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/gtw.GTWCalendarWrapper"
                        }
                    }
                }
            }
        },
        "/v1/packages/{packageToken}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Get package",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Packages"
                ],
                "summary": "Get package by token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "package token",
                        "name": "packageToken",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/gtw.GTWPackage"
                        }
                    }
                }
            }
        },
        "/v1/packages/{packageToken}/dcr": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Get package dcr",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Packages"
                ],
                "summary": "Get package dcr by token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "package token",
                        "name": "packageToken",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/gtw.GTWDcr"
                        }
                    }
                }
            }
        },
        "/v1/packages/has-avail/{rateToken}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "Has Avail",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Packages"
                ],
                "summary": "Has Avail by Rate Token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Rate Token",
                        "name": "rateToken",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/gtw.GTWPackageHasAvail"
                        }
                    }
                }
            }
        },
    },
    "definitions": {
        "entities.Metrics": {
            "type": "object",
            "properties": {
                "promotions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/entities.MetricsResult"
                    }
                },
                "result": {
                    "$ref": "#/definitions/entities.MetricsResult"
                }
            }
        },
        "entities.MetricsResult": {
            "type": "object",
            "properties": {
                "duration": {
                    "type": "integer"
                },
                "statusCode": {
                    "type": "integer"
                }
            }
        },
        "gtw.GTWAirportLocation": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWAirports": {
            "type": "object",
            "properties": {
                "iata": {
                    "type": "string"
                },
                "location": {
                    "$ref": "#/definitions/gtw.GTWAirportLocation"
                },
                "name": {
                    "type": "string"
                },
                "stop": {
                    "type": "boolean"
                }
            }
        },
        "gtw.GTWAmenities": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWBaggage": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "isIncluded": {
                    "type": "boolean"
                },
                "quantity": {
                    "type": "number"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWCalendar": {
            "type": "object",
            "properties": {
                "bestPrice": {
                    "type": "number"
                },
                "bestPriceWithTax": {
                    "type": "number"
                },
                "date": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWCalendarWrapper": {
            "type": "object",
            "properties": {
                "calendar": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWCalendar"
                    }
                }
            }
        },
        "gtw.GTWCoordinates": {
            "type": "object",
            "properties": {
                "latitude": {
                    "type": "string"
                },
                "longitude": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWDcr": {
            "type": "object",
            "properties": {
                "flight": {
                    "$ref": "#/definitions/gtw.GTWDcrValues"
                },
                "hotels": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWDcrValues"
                    }
                }
            }
        },
        "gtw.GTWDcrValues": {
            "type": "object",
            "properties": {
                "available": {
                    "type": "integer"
                },
                "blocked": {
                    "type": "integer"
                },
                "confirmed": {
                    "type": "integer"
                },
                "reserved": {
                    "type": "integer"
                }
            }
        },
        "gtw.GTWDestination": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "links": {
                    "$ref": "#/definitions/gtw.GTWLinks"
                },
                "location": {
                    "$ref": "#/definitions/gtw.GTWPackageLocation"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWTags"
                    }
                },
                "warning": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWFareGroup": {
            "type": "object",
            "properties": {
                "currency": {
                    "type": "string"
                },
                "fares": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWFares"
                    }
                },
                "playerPriceWithTax": {
                    "type": "number"
                },
                "playerPriceWithoutTax": {
                    "type": "number"
                },
                "priceWithTax": {
                    "type": "number"
                },
                "priceWithoutTax": {
                    "type": "number"
                }
            }
        },
        "gtw.GTWFareProfile": {
            "type": "object",
            "properties": {
                "baggages": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWBaggage"
                    }
                },
                "services": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWServices"
                    }
                }
            }
        },
        "gtw.GTWFares": {
            "type": "object",
            "properties": {
                "passengersCount": {
                    "type": "integer"
                },
                "passengersType": {
                    "type": "string"
                },
                "priceWithTax": {
                    "type": "number"
                },
                "priceWithoutTax": {
                    "type": "number"
                },
                "taxes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWFlightTaxes"
                    }
                }
            }
        },
        "gtw.GTWFlight": {
            "type": "object",
            "properties": {
                "airports": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWAirports"
                    }
                },
                "fareGroup": {
                    "$ref": "#/definitions/gtw.GTWFareGroup"
                },
                "id": {
                    "type": "string"
                },
                "segments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWSegments"
                    }
                },
                "validatingBy": {
                    "$ref": "#/definitions/gtw.GTWValidatingBy"
                }
            }
        },
        "gtw.GTWFlightTaxes": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "number"
                },
                "code": {
                    "type": "string"
                },
                "currency": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "values": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWFlightTaxesValue"
                    }
                }
            }
        },
        "gtw.GTWFlightTaxesValue": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "number"
                },
                "currency": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWHotel": {
            "type": "object",
            "properties": {
                "amenities": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWAmenities"
                    }
                },
                "award": {
                    "type": "integer"
                },
                "checkIn": {
                    "type": "string"
                },
                "checkOut": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "isPlus": {
                    "type": "boolean"
                },
                "isPreferential": {
                    "type": "boolean"
                },
                "links": {
                    "$ref": "#/definitions/gtw.GTWLinks"
                },
                "location": {
                    "$ref": "#/definitions/gtw.GTWLocations"
                },
                "name": {
                    "type": "string"
                },
                "numberOfNights": {
                    "type": "integer"
                },
                "priority": {
                    "type": "integer"
                },
                "rooms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWRooms"
                    }
                }
            }
        },
        "gtw.GTWHotelTaxes": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "number"
                },
                "description": {
                    "type": "string"
                },
                "percent": {
                    "type": "number"
                }
            }
        },
        "gtw.GTWIataList": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "iata": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWImages": {
            "type": "object",
            "properties": {
                "href": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWIncludedService": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWItineraryDayByDay": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "label": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWLegs": {
            "type": "object",
            "properties": {
                "aircraftCode": {
                    "type": "string"
                },
                "arrival": {
                    "type": "string"
                },
                "arrivalDate": {
                    "type": "string"
                },
                "departure": {
                    "type": "string"
                },
                "departureDate": {
                    "type": "string"
                },
                "duration": {
                    "type": "integer"
                },
                "fareBasis": {
                    "type": "string"
                },
                "fareClass": {
                    "type": "string"
                },
                "flightCode": {
                    "type": "string"
                },
                "flightNumber": {
                    "type": "string"
                },
                "numberOfStops": {
                    "type": "integer"
                },
                "seatClass": {
                    "$ref": "#/definitions/gtw.GTWSeatClass"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWLinks": {
            "type": "object",
            "properties": {
                "images": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWImages"
                    }
                },
                "thumbnailImage": {
                    "$ref": "#/definitions/gtw.GTWThumbnailImage"
                }
            }
        },
        "gtw.GTWLocation": {
            "type": "object",
            "properties": {
                "city": {
                    "$ref": "#/definitions/gtw.GTWLocationCity"
                },
                "description": {
                    "type": "string"
                },
                "iata": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "locationProducts": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWLocationProducts"
                    }
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWLocationCity": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "state": {
                    "$ref": "#/definitions/gtw.GTWLocationState"
                }
            }
        },
        "gtw.GTWLocationCountry": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "shortName": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWLocationProducts": {
            "type": "object",
            "properties": {
                "iataList": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWIataList"
                    }
                }
            }
        },
        "gtw.GTWLocationState": {
            "type": "object",
            "properties": {
                "country": {
                    "$ref": "#/definitions/gtw.GTWLocationCountry"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "shortName": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWLocations": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "coordinates": {
                    "$ref": "#/definitions/gtw.GTWCoordinates"
                }
            }
        },
        "gtw.GTWMeta": {
            "type": "object",
            "properties": {
                "categories": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWMetaCategories"
                    }
                },
                "hotels": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWMetaHotels"
                    }
                },
                "mealPlans": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWMetaMealPlans"
                    }
                },
                "totalPackageValue": {
                    "$ref": "#/definitions/gtw.GTWMetaTotalPackageValue"
                },
                "totalPackages": {
                    "type": "integer"
                },
                "totalPages": {
                    "type": "integer"
                }
            }
        },
        "gtw.GTWMetaCategories": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWMetaHotels": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWMetaMealPlans": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWMetaTotalPackageValue": {
            "type": "object",
            "properties": {
                "max": {
                    "type": "number"
                },
                "min": {
                    "type": "number"
                }
            }
        },
        "gtw.GTWOrigin": {
            "type": "object",
            "properties": {
                "location": {
                    "$ref": "#/definitions/gtw.GTWPackageLocation"
                }
            }
        },
        "gtw.GTWPackage": {
            "type": "object",
            "properties": {
                "airProtection": {
                    "type": "boolean"
                },
                "groupReserve": {
                    "type": "boolean"
                },
                "checkIn": {
                    "type": "string"
                },
                "checkOut": {
                    "type": "string"
                },
                "days": {
                    "type": "integer"
                },
                "dcr": {
                    "$ref": "#/definitions/gtw.GTWDcr"
                },
                "destination": {
                    "$ref": "#/definitions/gtw.GTWDestination"
                },
                "flights": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWFlight"
                    }
                },
                "hotels": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWHotel"
                    }
                },
                "id": {
                    "type": "string"
                },
                "includedServices": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWIncludedService"
                    }
                },
                "isLandCombo": {
                    "type": "boolean"
                },
                "itineraryDayByDay": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWItineraryDayByDay"
                    }
                },
                "links": {
                    "$ref": "#/definitions/gtw.GTWLinks"
                },
                "origin": {
                    "$ref": "#/definitions/gtw.GTWOrigin"
                },
                "packageToken": {
                    "type": "string"
                },
                "products": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWPackageLocation": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string"
                },
                "coordinates": {
                    "$ref": "#/definitions/gtw.GTWCoordinates"
                },
                "country": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWPackagesResponse": {
            "type": "object",
            "properties": {
                "meta": {
                    "$ref": "#/definitions/gtw.GTWMeta"
                },
                "metrics": {
                    "$ref": "#/definitions/entities.Metrics"
                },
                "packages": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWPackage"
                    }
                }
            }
        },
        "gtw.GTWRates": {
            "type": "object",
            "properties": {
                "currency": {
                    "type": "string"
                },
                "packageGroup": {
                    "type": "string"
                },
                "pricePerDayWithTax": {
                    "type": "number"
                },
                "pricePerDayWithoutTax": {
                    "type": "number"
                },
                "pricePerPaxWithTax": {
                    "type": "number"
                },
                "pricePerPaxWithoutTax": {
                    "type": "number"
                },
                "priceWithTax": {
                    "type": "number"
                },
                "priceWithoutTax": {
                    "type": "number"
                },
                "profit": {
                    "type": "number"
                },
                "promotionItem": {
                    "$ref": "#/definitions/gtw.PromotionItem"
                },
                "rateToken": {
                    "type": "string"
                },
                "taxes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWHotelTaxes"
                    }
                }
            }
        },
        "gtw.GTWRooms": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string"
                },
                "mealPlan": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "rates": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWRates"
                    }
                },
                "rph": {
                    "type": "integer"
                }
            }
        },
        "gtw.GTWSeatClass": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWSegments": {
            "type": "object",
            "properties": {
                "arrival": {
                    "type": "string"
                },
                "arrivalDate": {
                    "type": "string"
                },
                "departure": {
                    "type": "string"
                },
                "departureDate": {
                    "type": "string"
                },
                "duration": {
                    "type": "integer"
                },
                "fareProfile": {
                    "$ref": "#/definitions/gtw.GTWFareProfile"
                },
                "fareType": {
                    "type": "string"
                },
                "legs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/gtw.GTWLegs"
                    }
                },
                "numberOfStops": {
                    "type": "integer"
                },
                "packageGroup": {
                    "type": "string"
                },
                "rateToken": {
                    "type": "string"
                },
                "routeRPH": {
                    "type": "integer"
                },
                "rph": {
                    "type": "integer"
                }
            }
        },
        "gtw.GTWServices": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "isIncluded": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWTags": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWThumbnailImage": {
            "type": "object",
            "properties": {
                "href": {
                    "type": "string"
                }
            }
        },
        "gtw.GTWValidatingBy": {
            "type": "object",
            "properties": {
                "iata": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "gtw.Promotion": {
            "type": "object",
            "properties": {
                "discountApplied": {
                    "type": "string"
                },
                "percentage": {
                    "type": "number"
                },
                "pricePerDayWithTax": {
                    "type": "number"
                },
                "pricePerDayWithoutTax": {
                    "type": "number"
                },
                "priceWithTax": {
                    "type": "number"
                },
                "priceWithoutTax": {
                    "type": "number"
                }
            }
        },
        "gtw.PromotionItem": {
            "type": "object",
            "properties": {
                "hasCombo": {
                    "type": "boolean"
                },
                "keyRateToken": {
                    "type": "string"
                },
                "promotion": {
                    "$ref": "#/definitions/gtw.Promotion"
                },
                "rateToken": {
                    "type": "string"
                },
                "rph": {
                    "type": "integer"
                }
            }
        },
        "request.FilterInputRange": {
            "type": "object",
            "properties": {
                "Max": {
                    "type": "number"
                },
                "Min": {
                    "type": "number"
                }
            }
        },
        "request.RequestPromoItem": {
            "type": "object",
            "properties": {
                "group": {
                    "type": "string"
                },
                "rateToken": {
                    "type": "string"
                },
                "rph": {
                    "type": "integer"
                }
            }
        },
        "request.RequestPromoParams": {
            "type": "object",
            "properties": {
                "cpf": {
                    "type": "string"
                },
                "points": {
                    "type": "integer"
                },
                "promoCode": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "services": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "showDetail": {
                    "type": "boolean"
                },
                "showPayloads": {
                    "type": "boolean"
                },
                "showSteps": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                },
                "wait": {
                    "type": "integer"
                }
            }
        },
        "request.RequestSearchPackages": {
            "type": "object",
            "properties": {
                "destination": {
                    "type": "string"
                },
                "endCheckInDate": {
                    "type": "string",
                    "example": "YYYY-MM-DD"
                },
                "filters": {
                    "$ref": "#/definitions/request.RequestSearchPackagesFilter"
                },
                "origin": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "promotion": {
                    "$ref": "#/definitions/request.RequestSearchPackagesPromotion"
                },
                "rooms": {
                    "type": "string",
                    "example": "30,30"
                },
                "size": {
                    "type": "integer"
                },
                "sortBy": {
                    "type": "string",
                    "enum": [
                        "lower_price",
                        "higher_price"
                    ]
                },
                "startCheckInDate": {
                    "type": "string",
                    "example": "YYYY-MM-DD"
                }
            }
        },
        "request.RequestSearchPackagesFilter": {
            "type": "object",
            "properties": {
                "categories": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "hotelName": {
                    "type": "string"
                },
                "mealPlans": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "totalPackageValue": {
                    "$ref": "#/definitions/request.FilterInputRange"
                }
            }
        },
        "request.RequestSearchPackagesPromotion": {
            "type": "object",
            "properties": {
                "params": {
                    "$ref": "#/definitions/request.RequestPromoParams"
                },
                "selectedItems": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/request.RequestPromoItem"
                    }
                }
            }
        },
        "gtw.GTWPackagePrice":{
          "type": "object",
          "properties": {
            "packageGroup": {"type": "string"},
            "currency": {"type": "string"},
            "priceWithTax": {"type": "number"},
            "priceWithoutTax": {"type": "number"},
            "pricePerDayWithTax":{"type": "number"},
            "pricePerDayWithoutTax":{"type": "number"},
            "pricePerPaxWithTax":{"type": "number"},
            "pricePerPaxWithoutTax":{"type": "number"},
          }
        },
        "gtw.GTWPackageHasAvail":{
          "type": "object",
          "properties": {
            "packageRateToken": {"type": "string"},
            "packagePrice": {
              "$ref": "#/definitions/gtw.GTWPackagePrice"
            },
            "groupReserve": {"type": "boolean"}
          }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "type": "apiKey",
            "name": "access_token",
            "in": "header"
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "1.0",
	Host:        "",
	BasePath:    "",
	Schemes:     []string{},
	Title:       "bff-go-shopping-static-store",
	Description: "",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
		"escape": func(v interface{}) string {
			// escape tabs
			str := strings.Replace(v.(string), "\t", "\\t", -1)
			// replace " with \", and if that results in \\", replace that with \\\"
			str = strings.Replace(str, "\"", "\\\"", -1)
			return strings.Replace(str, "\\\\\"", "\\\\\\\"", -1)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register("swagger", &s{})
}
