definitions:
  entities.Metrics:
    properties:
      promotions:
        items:
          $ref: '#/definitions/entities.MetricsResult'
        type: array
      result:
        $ref: '#/definitions/entities.MetricsResult'
    type: object
  entities.MetricsResult:
    properties:
      duration:
        type: integer
      statusCode:
        type: integer
    type: object
  gtw.GTWAirportLocation:
    properties:
      name:
        type: string
    type: object
  gtw.GTWAirports:
    properties:
      iata:
        type: string
      location:
        $ref: '#/definitions/gtw.GTWAirportLocation'
      name:
        type: string
      stop:
        type: boolean
    type: object
  gtw.GTWAmenities:
    properties:
      code:
        type: string
      name:
        type: string
    type: object
  gtw.GTWBaggage:
    properties:
      description:
        type: string
      isIncluded:
        type: boolean
      quantity:
        type: number
      type:
        type: string
    type: object
  gtw.GTWCalendar:
    properties:
      bestPrice:
        type: number
      bestPriceWithTax:
        type: number
      date:
        type: string
    type: object
  gtw.GTWCalendarWrapper:
    properties:
      calendar:
        items:
          $ref: '#/definitions/gtw.GTWCalendar'
        type: array
    type: object
  gtw.GTWCoordinates:
    properties:
      latitude:
        type: string
      longitude:
        type: string
    type: object
  gtw.GTWDcr:
    properties:
      flight:
        $ref: '#/definitions/gtw.GTWDcrValues'
      hotels:
        items:
          $ref: '#/definitions/gtw.GTWDcrValues'
        type: array
    type: object
  gtw.GTWDcrValues:
    properties:
      available:
        type: integer
      blocked:
        type: integer
      confirmed:
        type: integer
      reserved:
        type: integer
    type: object
  gtw.GTWDestination:
    properties:
      description:
        type: string
      id:
        type: string
      links:
        $ref: '#/definitions/gtw.GTWLinks'
      location:
        $ref: '#/definitions/gtw.GTWPackageLocation'
      tags:
        items:
          $ref: '#/definitions/gtw.GTWTags'
        type: array
      warning:
        type: string
    type: object
  gtw.GTWFareGroup:
    properties:
      currency:
        type: string
      fares:
        items:
          $ref: '#/definitions/gtw.GTWFares'
        type: array
      playerPriceWithTax:
        type: number
      playerPriceWithoutTax:
        type: number
      priceWithTax:
        type: number
      priceWithoutTax:
        type: number
    type: object
  gtw.GTWFareProfile:
    properties:
      baggages:
        items:
          $ref: '#/definitions/gtw.GTWBaggage'
        type: array
      services:
        items:
          $ref: '#/definitions/gtw.GTWServices'
        type: array
    type: object
  gtw.GTWFares:
    properties:
      passengersCount:
        type: integer
      passengersType:
        type: string
      priceWithTax:
        type: number
      priceWithoutTax:
        type: number
      taxes:
        items:
          $ref: '#/definitions/gtw.GTWFlightTaxes'
        type: array
    type: object
  gtw.GTWFlight:
    properties:
      airports:
        items:
          $ref: '#/definitions/gtw.GTWAirports'
        type: array
      fareGroup:
        $ref: '#/definitions/gtw.GTWFareGroup'
      id:
        type: string
      segments:
        items:
          $ref: '#/definitions/gtw.GTWSegments'
        type: array
      validatingBy:
        $ref: '#/definitions/gtw.GTWValidatingBy'
    type: object
  gtw.GTWFlightTaxes:
    properties:
      amount:
        type: number
      code:
        type: string
      currency:
        type: string
      description:
        type: string
      values:
        items:
          $ref: '#/definitions/gtw.GTWFlightTaxesValue'
        type: array
    type: object
  gtw.GTWFlightTaxesValue:
    properties:
      amount:
        type: number
      currency:
        type: string
    type: object
  gtw.GTWHotel:
    properties:
      amenities:
        items:
          $ref: '#/definitions/gtw.GTWAmenities'
        type: array
      award:
        type: integer
      checkIn:
        type: string
      checkOut:
        type: string
      description:
        type: string
      id:
        type: string
      isPlus:
        type: boolean
      isPreferential:
        type: boolean
      links:
        $ref: '#/definitions/gtw.GTWLinks'
      location:
        $ref: '#/definitions/gtw.GTWLocations'
      name:
        type: string
      numberOfNights:
        type: integer
      priority:
        type: integer
      rooms:
        items:
          $ref: '#/definitions/gtw.GTWRooms'
        type: array
    type: object
  gtw.GTWHotelTaxes:
    properties:
      amount:
        type: number
      description:
        type: string
      percent:
        type: number
    type: object
  gtw.GTWIataList:
    properties:
      description:
        type: string
      iata:
        type: string
      name:
        type: string
    type: object
  gtw.GTWImages:
    properties:
      href:
        type: string
    type: object
  gtw.GTWIncludedService:
    properties:
      code:
        type: string
      description:
        type: string
      name:
        type: string
    type: object
  gtw.GTWItineraryDayByDay:
    properties:
      description:
        type: string
      label:
        type: string
    type: object
  gtw.GTWLegs:
    properties:
      aircraftCode:
        type: string
      arrival:
        type: string
      arrivalDate:
        type: string
      departure:
        type: string
      departureDate:
        type: string
      duration:
        type: integer
      fareBasis:
        type: string
      fareClass:
        type: string
      flightCode:
        type: string
      flightNumber:
        type: string
      numberOfStops:
        type: integer
      seatClass:
        $ref: '#/definitions/gtw.GTWSeatClass'
      status:
        type: string
    type: object
  gtw.GTWLinks:
    properties:
      images:
        items:
          $ref: '#/definitions/gtw.GTWImages'
        type: array
      thumbnailImage:
        $ref: '#/definitions/gtw.GTWThumbnailImage'
    type: object
  gtw.GTWLocation:
    properties:
      city:
        $ref: '#/definitions/gtw.GTWLocationCity'
      description:
        type: string
      iata:
        type: string
      id:
        type: string
      locationProducts:
        items:
          $ref: '#/definitions/gtw.GTWLocationProducts'
        type: array
      name:
        type: string
    type: object
  gtw.GTWLocationCity:
    properties:
      id:
        type: string
      name:
        type: string
      state:
        $ref: '#/definitions/gtw.GTWLocationState'
    type: object
  gtw.GTWLocationCountry:
    properties:
      id:
        type: string
      name:
        type: string
      shortName:
        type: string
    type: object
  gtw.GTWLocationProducts:
    properties:
      iataList:
        items:
          $ref: '#/definitions/gtw.GTWIataList'
        type: array
    type: object
  gtw.GTWLocationState:
    properties:
      country:
        $ref: '#/definitions/gtw.GTWLocationCountry'
      id:
        type: string
      name:
        type: string
      shortName:
        type: string
    type: object
  gtw.GTWLocations:
    properties:
      address:
        type: string
      coordinates:
        $ref: '#/definitions/gtw.GTWCoordinates'
    type: object
  gtw.GTWMeta:
    properties:
      categories:
        items:
          $ref: '#/definitions/gtw.GTWMetaCategories'
        type: array
      hotels:
        items:
          $ref: '#/definitions/gtw.GTWMetaHotels'
        type: array
      mealPlans:
        items:
          $ref: '#/definitions/gtw.GTWMetaMealPlans'
        type: array
      totalPackageValue:
        $ref: '#/definitions/gtw.GTWMetaTotalPackageValue'
      totalPackages:
        type: integer
      totalPages:
        type: integer
    type: object
  gtw.GTWMetaCategories:
    properties:
      count:
        type: integer
      name:
        type: string
    type: object
  gtw.GTWMetaHotels:
    properties:
      id:
        type: string
      name:
        type: string
    type: object
  gtw.GTWMetaMealPlans:
    properties:
      count:
        type: integer
      name:
        type: string
    type: object
  gtw.GTWMetaTotalPackageValue:
    properties:
      max:
        type: number
      min:
        type: number
    type: object
  gtw.GTWOrigin:
    properties:
      location:
        $ref: '#/definitions/gtw.GTWPackageLocation'
    type: object
  gtw.GTWPackage:
    properties:
      airProtection:
        type: boolean
      groupReserve:
        type: boolean
      checkIn:
        type: string
      checkOut:
        type: string
      days:
        type: integer
      dcr:
        $ref: '#/definitions/gtw.GTWDcr'
      destination:
        $ref: '#/definitions/gtw.GTWDestination'
      flights:
        items:
          $ref: '#/definitions/gtw.GTWFlight'
        type: array
      hotels:
        items:
          $ref: '#/definitions/gtw.GTWHotel'
        type: array
      id:
        type: string
      includedServices:
        items:
          $ref: '#/definitions/gtw.GTWIncludedService'
        type: array
      isLandCombo:
        type: boolean
      itineraryDayByDay:
        items:
          $ref: '#/definitions/gtw.GTWItineraryDayByDay'
        type: array
      links:
        $ref: '#/definitions/gtw.GTWLinks'
      origin:
        $ref: '#/definitions/gtw.GTWOrigin'
      packageToken:
        type: string
      products:
        items:
          type: string
        type: array
      title:
        type: string
    type: object
  gtw.GTWPackageLocation:
    properties:
      city:
        type: string
      coordinates:
        $ref: '#/definitions/gtw.GTWCoordinates'
      country:
        type: string
      id:
        type: string
      state:
        type: string
    type: object
  gtw.GTWPackagesResponse:
    properties:
      meta:
        $ref: '#/definitions/gtw.GTWMeta'
      metrics:
        $ref: '#/definitions/entities.Metrics'
      packages:
        items:
          $ref: '#/definitions/gtw.GTWPackage'
        type: array
    type: object
  gtw.GTWRates:
    properties:
      currency:
        type: string
      packageGroup:
        type: string
      pricePerDayWithTax:
        type: number
      pricePerDayWithoutTax:
        type: number
      pricePerPaxWithTax:
        type: number
      pricePerPaxWithoutTax:
        type: number
      priceWithTax:
        type: number
      priceWithoutTax:
        type: number
      profit:
        type: number
      promotionItem:
        $ref: '#/definitions/gtw.PromotionItem'
      rateToken:
        type: string
      taxes:
        items:
          $ref: '#/definitions/gtw.GTWHotelTaxes'
        type: array
    type: object
  gtw.GTWRooms:
    properties:
      category:
        type: string
      mealPlan:
        type: string
      name:
        type: string
      rates:
        items:
          $ref: '#/definitions/gtw.GTWRates'
        type: array
      rph:
        type: integer
    type: object
  gtw.GTWSeatClass:
    properties:
      code:
        type: string
      description:
        type: string
    type: object
  gtw.GTWSegments:
    properties:
      arrival:
        type: string
      arrivalDate:
        type: string
      departure:
        type: string
      departureDate:
        type: string
      duration:
        type: integer
      fareProfile:
        $ref: '#/definitions/gtw.GTWFareProfile'
      fareType:
        type: string
      legs:
        items:
          $ref: '#/definitions/gtw.GTWLegs'
        type: array
      numberOfStops:
        type: integer
      packageGroup:
        type: string
      rateToken:
        type: string
      routeRPH:
        type: integer
      rph:
        type: integer
    type: object
  gtw.GTWServices:
    properties:
      description:
        type: string
      isIncluded:
        type: boolean
      type:
        type: string
    type: object
  gtw.GTWTags:
    properties:
      id:
        type: string
      name:
        type: string
    type: object
  gtw.GTWThumbnailImage:
    properties:
      href:
        type: string
    type: object
  gtw.GTWValidatingBy:
    properties:
      iata:
        type: string
      name:
        type: string
    type: object
  gtw.Promotion:
    properties:
      discountApplied:
        type: string
      percentage:
        type: number
      pricePerDayWithTax:
        type: number
      pricePerDayWithoutTax:
        type: number
      priceWithTax:
        type: number
      priceWithoutTax:
        type: number
    type: object
  gtw.PromotionItem:
    properties:
      hasCombo:
        type: boolean
      keyRateToken:
        type: string
      promotion:
        $ref: '#/definitions/gtw.Promotion'
      rateToken:
        type: string
      rph:
        type: integer
    type: object
  request.FilterInputRange:
    properties:
      Max:
        type: number
      Min:
        type: number
    type: object
  request.RequestPromoItem:
    properties:
      group:
        type: string
      rateToken:
        type: string
      rph:
        type: integer
    type: object
  request.RequestPromoParams:
    properties:
      cpf:
        type: string
      points:
        type: integer
      promoCode:
        items:
          type: string
        type: array
      services:
        items:
          type: string
        type: array
      showDetail:
        type: boolean
      showPayloads:
        type: boolean
      showSteps:
        type: boolean
      type:
        type: string
      wait:
        type: integer
    type: object
  request.RequestSearchPackages:
    properties:
      destination:
        type: string
      endCheckInDate:
        example: YYYY-MM-DD
        type: string
      filters:
        $ref: '#/definitions/request.RequestSearchPackagesFilter'
      origin:
        type: string
      page:
        type: integer
      promotion:
        $ref: '#/definitions/request.RequestSearchPackagesPromotion'
      rooms:
        example: 30,30
        type: string
      size:
        type: integer
      sortBy:
        enum:
        - lower_price
        - higher_price
        type: string
      startCheckInDate:
        example: YYYY-MM-DD
        type: string
    type: object
  request.RequestSearchPackagesFilter:
    properties:
      categories:
        items:
          type: string
        type: array
      hotelName:
        type: string
      mealPlans:
        items:
          type: string
        type: array
      totalPackageValue:
        $ref: '#/definitions/request.FilterInputRange'
    type: object
  request.RequestSearchPackagesPromotion:
    properties:
      params:
        $ref: '#/definitions/request.RequestPromoParams'
      selectedItems:
        items:
          $ref: '#/definitions/request.RequestPromoItem'
        type: array
    type: object
info:
  contact: {}
  title: bff-go-shopping-static-store
  version: "1.0"
paths:
  /v1/alternative-packages:
    get:
      consumes:
      - application/json
      description: Search alternative packages
      parameters:
      - description: id origin
        in: query
        name: origin
        required: true
        type: string
      - description: id destination
        in: query
        name: destination
        required: true
        type: string
      - description: 'check-in start date (ex: 2022-01-01)'
        in: query
        name: startCheckIn
        required: true
        type: string
      - description: 'check-in end date (ex: 2022-01-31)'
        in: query
        name: endCheckIn
        required: true
        type: string
      - description: 'rooms (ex: 30,30)'
        in: query
        name: rooms
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/gtw.GTWPackage'
            type: array
      security:
      - ApiKeyAuth: []
      summary: Search alternative packages
      tags:
      - Packages
  /v1/locations:
    get:
      consumes:
      - application/json
      description: all locations
      parameters:
      - description: filter by name
        in: query
        name: name
        type: string
      - description: records per page (default 100)
        in: query
        name: size
        type: integer
      - description: search origins only
        in: query
        name: isOrigin
        type: boolean
      - description: Id of origin
        in: query
        name: origin
        type: string
      - description: search destinations only
        in: query
        name: isDestination
        type: boolean
      - description: 'Id of destination '
        in: query
        name: destination
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/gtw.GTWLocation'
            type: array
      security:
      - ApiKeyAuth: []
      summary: Get all locations
      tags:
      - Location
  /v1/packages:
    post:
      consumes:
      - application/json
      description: Search packages
      parameters:
      - description: Search body request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/request.RequestSearchPackages'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/gtw.GTWPackagesResponse'
      security:
      - ApiKeyAuth: []
      summary: Search packages
      tags:
      - Packages
  /v1/packages/{packageToken}:
    get:
      consumes:
      - application/json
      description: Get package
      parameters:
      - description: package token
        in: path
        name: packageToken
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/gtw.GTWPackage'
      security:
      - ApiKeyAuth: []
      summary: Get package by token
      tags:
      - Packages
  /v1/packages/{packageToken}/dcr:
    get:
      consumes:
      - application/json
      description: Get package dcr
      parameters:
      - description: package token
        in: path
        name: packageToken
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/gtw.GTWDcr'
      security:
      - ApiKeyAuth: []
      summary: Get package dcr by token
      tags:
      - Packages
  /v1/packages/advanced-search:
    post:
      consumes:
      - application/json
      description: Search alternative packages
      parameters:
      - description: id flightsGroupUserId
        in: query
        name: flightsGroupUserId
        required: true
        type: string
      - description: id reservationCode
        in: query
        name: reservationCode
        required: true
        type: string
      - description: id tourCode
        in: query
        name: tourCode
        required: true
        type: string
      - description: 'departure date (ex: 2022-01-01)'
        in: query
        name: departureDate
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/gtw.GTWPackage'
            type: array
      security:
      - ApiKeyAuth: []
      summary: Search alternative packages
      tags:
      - Packages
  /v1/packages/calendar:
    get:
      consumes:
      - application/json
      description: Get calendar
      parameters:
      - description: id origin
        in: query
        name: origin
        required: true
        type: string
      - description: id destination
        in: query
        name: destination
        required: true
        type: string
      - description: check-in start date (YYYY-MM-DD)
        in: query
        name: startCheckIn
        required: true
        type: string
      - description: qtd of months
        in: query
        name: numberOfMonths
        required: true
        type: integer
      - description: 'rooms (ex: 30,30)'
        in: query
        name: rooms
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/gtw.GTWCalendarWrapper'
      security:
      - ApiKeyAuth: []
      summary: Get calendar
      tags:
      - Packages
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: access_token
    type: apiKey
swagger: "2.0"
