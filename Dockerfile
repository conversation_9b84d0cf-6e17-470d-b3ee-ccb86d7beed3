# Build Stage para Produção
FROM public.ecr.aws/e7n1h3b8/golang:1.17.5-alpine3.15 as builder
WORKDIR /app

RUN apk update && apk upgrade && apk --update add git make && apk add --no-cache openssh

COPY go.mod go.sum ./
RUN go mod download
COPY . .

# Build the Go app para Produção
RUN go build -o engine main.go

# Build Stage para Veracode
FROM public.ecr.aws/e7n1h3b8/golang:1.17.5-alpine3.15 as veracode_builder
WORKDIR /app
RUN apk update && apk upgrade && apk --update add git make zip openssh gcc musl-dev build-base

COPY go.mod go.sum ./
RUN go mod download
COPY . .

# Criar a pasta vendor com as dependências
RUN go mod vendor

# Build the Go app para Veracode com símbolos de depuração
RUN go build -gcflags "all=-N -l" -mod=vendor -o engine main.go

# Criação do arquivo zip para Veracode
RUN mkdir app && mv engine app/ && mv vendor app/ && cp go.mod go.sum app/
RUN zip -r bff-go-shopping-static-store.zip app

# Veracode Stage
FROM veracode/api-wrapper-java:latest as veracode
ARG VERACODE_APP_ID
ARG VERACODE_API_KEY
ARG BUILD_ID
WORKDIR /app
COPY --from=veracode_builder /app/bff-go-shopping-static-store.zip /app/bff-go-shopping-static-store.zip

# Executar o Veracode UploadAndScan
RUN java -jar /opt/veracode/api-wrapper.jar \
    -vid $VERACODE_APP_ID \
    -vkey $VERACODE_API_KEY \
    -version $BUILD_ID \
    -action UploadAndScan \
    -createprofile true \
    -appname "bff-go-shopping-static-store" \
    -filepath /app/bff-go-shopping-static-store.zip; exit 0;

# Final Stage para Produção
FROM alpine:latest
RUN apk update && apk upgrade && apk --update --no-cache add tzdata && mkdir /app

WORKDIR /app
COPY --from=builder /app/engine /app

EXPOSE 9090
CMD /app/engine
LABEL Name=bff-go-shopping-static-store Version=v1.0.0
