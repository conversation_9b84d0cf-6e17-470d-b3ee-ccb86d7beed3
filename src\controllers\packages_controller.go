package controllers

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/entities/request"
	"bff-go-shopping-static-store/src/services"
	"bff-go-shopping-static-store/src/util"

	"github.com/labstack/echo/v4"
)

type PackagesController struct {
	appPackageService services.APPPackagesService
}

func NewPackagesController(_router *echo.Echo, _appPacakgeService *services.APPPackagesService) {
	handler := &PackagesController{
		appPackageService: *_appPacakgeService,
	}

	_router.GET("/v1/packages/calendar", handler.getCalendar)
	_router.GET("/v1/packages/:packageToken/dcr", handler.getPackageDCR)
	_router.GET("/v1/packages/:packageToken", handler.getPackageByToken)
	_router.GET("/v1/packages/has-avail/:rateToken", handler.getAvailPackageByRateToken)
	_router.POST("/v1/packages", handler.searchPackages)
	_router.GET("/v1/alternative-packages", handler.getAlternativePackages)
	_router.GET("/v1/packages/advanced-search", handler.getAdvancedSearch)
}

// Search packages godoc
// @Summary Search packages
// @Description Search packages
// @Tags Packages
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} gtw.GTWPackagesResponse
// @Param body body request.RequestSearchPackages true "Search body request"
// @Router /v1/packages [post]
func (handler *PackagesController) searchPackages(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "PackagesController.searchPackages"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting searchPackages")

	header := GetHeader(ctx)
	request := new(request.RequestSearchPackages)
	if err := ctx.Bind(request); err != nil {
		logger.ErrorWithContext(logCtx, err, source, "error", "error parse request body")
		restError := util.NewBadRequestError(err.Error())
		return ctx.JSON(restError.StatusCode, restError)
	}

	strRequest, _ := json.Marshal(request)
	logger.InfoWithContext(logCtx, source, "request body", string(strRequest))

	resp, err := handler.appPackageService.SearchPackages(header, request, logCtx)
	if err != nil {
		restError := util.NewGenericError("Error searching packages")
		return ctx.JSON(restError.StatusCode, restError)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}

// Search alternative packages godoc
// @Summary Search alternative packages
// @Description Search alternative packages
// @Tags Packages
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} []gtw.GTWPackage
// @Param origin query string true "id origin"
// @Param destination query string true "id destination"
// @Param startCheckIn query string true "check-in start date (ex: 2022-01-01)"
// @Param endCheckIn query string true "check-in end date (ex: 2022-01-31)"
// @Param rooms query string true "rooms (ex: 30,30)"
// @Router /v1/alternative-packages [get]
func (handler *PackagesController) getAlternativePackages(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "PackagesController.getPackages"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getPackages")

	header := GetHeader(ctx)
	origin := ctx.QueryParam("origin")
	destination := ctx.QueryParam("destination")
	startCheckIn := ctx.QueryParam("startCheckIn")
	endCheckIn := ctx.QueryParam("endCheckIn")
	rooms := ctx.QueryParam("rooms")

	var productType *string = nil
	var includeAirProtection bool = true

	if ctx.QueryParam("includeAirProtection") == "false" {
		iap := false
		includeAirProtection = iap
	}

	if ctx.QueryParam("productType") != "" {
		pt := ctx.QueryParam("productType")
		productType = &pt
	}

	resp, err := handler.appPackageService.SearchAlternativePackages(header, origin, destination, startCheckIn, endCheckIn, rooms, productType, includeAirProtection, logCtx)
	if err != nil {
		restError := util.NewGenericError("Error searching alternative packages")
		return ctx.JSON(restError.StatusCode, restError)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}

// Get package by token godoc
// @Summary Get package by token
// @Description Get package
// @Tags Packages
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} gtw.GTWPackage
// @Param packageToken path string true "package token"
// @Router /v1/packages/{packageToken} [get]
func (handler *PackagesController) getPackageByToken(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "PackagesController.getPackageByToken"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getPackageByToken")

	header := GetHeader(ctx)
	packageToken := ctx.Param("packageToken")

	resp, err := handler.appPackageService.GetPackageByToken(header, packageToken, logCtx)
	if err != nil {
		restError := util.NewGenericError("Error searching package by token")
		return ctx.JSON(restError.StatusCode, restError)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}

// Get package dcr by token godoc
// @Summary Get package dcr by token
// @Description Get package dcr
// @Tags Packages
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} gtw.GTWDcr
// @Param packageToken path string true "package token"
// @Router /v1/packages/{packageToken}/dcr [get]
func (handler *PackagesController) getPackageDCR(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "PackagesController.getPackageDCR"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getPackageDCR")

	header := GetHeader(ctx)
	packageToken := ctx.Param("packageToken")

	resp, err := handler.appPackageService.GetDCR(header, packageToken, logCtx)
	if err != nil {
		restError := util.NewGenericError("Error searching DCR by token")
		return ctx.JSON(restError.StatusCode, restError)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}

// Get calendar godoc
// @Summary Get calendar
// @Description Get calendar
// @Tags Packages
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} gtw.GTWCalendarWrapper
// @Param origin query string true "id origin"
// @Param destination query string true "id destination"
// @Param startCheckIn query string true "check-in start date (YYYY-MM-DD)"
// @Param numberOfMonths query int true "qtd of months"
// @Param rooms query string true "rooms (ex: 30,30)"
// @Router /v1/packages/calendar [get]
func (handler *PackagesController) getCalendar(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "PackagesController.getCalendar"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getCalendar")

	header := GetHeader(ctx)
	origin := ctx.QueryParam("origin")
	destination := ctx.QueryParam("destination")
	startCheckIn := ctx.QueryParam("startCheckIn")
	rooms := ctx.QueryParam("rooms")
	numberOfMonths := 0
	if ctx.QueryParam("numberOfMonths") != "" {
		numberOfMonths, _ = strconv.Atoi(ctx.QueryParam("numberOfMonths"))
	}

	var productType *string = nil
	var includeAirProtection bool = true

	if ctx.QueryParam("productType") != "" {
		pt := ctx.QueryParam("productType")
		productType = &pt
	}

	if ctx.QueryParam("includeAirProtection") == "false" {
		iap := false
		includeAirProtection = iap
	}

	resp, err := handler.appPackageService.GetCalendar(header, origin, destination, startCheckIn, numberOfMonths, rooms, productType, includeAirProtection, logCtx)
	if err != nil {
		restError := util.NewGenericError("Error searching calendar")
		return ctx.JSON(restError.StatusCode, restError)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}

// Advanced Search packages godoc
// @Summary Search alternative packages
// @Description Search alternative packages
// @Tags Packages
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} []gtw.GTWPackage
// @Param flightsGroupUserId query string true "id flightsGroupUserId"
// @Param reservationCode query string true "id reservationCode"
// @Param tourCode query string true "id tourCode"
// @Param departureDate query string true "departure date (ex: 2022-01-01)"
// @Router /v1/packages/advanced-search [post]
func (handler *PackagesController) getAdvancedSearch(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "PackagesController.getAdvancedSearch"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getAdvancedSearch")

	header := GetHeader(ctx)
	request := new(request.RequestSearchPackages)

	paxes := ctx.QueryParam("paxes")
	departureDate := ctx.QueryParam("departureDate")
	flightsGroupUserId := ctx.QueryParam("flightsGroupUserId")
	reservationCode := ctx.QueryParam("reservationCode")
	tourCode := ctx.QueryParam("tourCode")
	packageId := ctx.QueryParam("packageId")

	resp, err := handler.appPackageService.AdvancedSearch(header, request, paxes, departureDate, flightsGroupUserId, reservationCode, tourCode, packageId, logCtx)
	if err != nil {
		return ctx.JSON(err.StatusCode, err)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}

// Call hasAvail Gateway
// @Summary Call hasAvail Gateway
// @Description Call hasAvail Gateway
// @Tags Packages
// @Accept json
// @Produce json
// @Consume json
// @Security ApiKeyAuth
// @Success 200 {object} gtw.GTWPackageHasAvail
// @Param rateToken query string true "rateToken"
// @Router /v1/packages/has-avail [get]
func (handler *PackagesController) getAvailPackageByRateToken(ctx echo.Context) error {
	logCtx := GetLoggerContext(ctx)

	source := "PackagesController.getAvailPackageByRateToken"
	defer logger.TimeElapsed(logCtx, time.Now(), source, "elapsed_time", "elapsed time")
	logger.InfoWithContext(logCtx, source, "start", "Starting getAvailPackageByRateToken")

	header := GetHeader(ctx)
	logger.Info(header)
	rateToken := ctx.Param("rateToken")

	resp, err := handler.appPackageService.HasAvailByRateToken(header, rateToken, logCtx)
	if err != nil {
		return ctx.JSON(err.StatusCode, err)
	}

	logger.InfoWithContext(logCtx, source, "success", "Success")
	return ctx.JSON(http.StatusOK, resp)
}
