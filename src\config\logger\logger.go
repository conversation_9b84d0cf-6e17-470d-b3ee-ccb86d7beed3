package logger

import (
	"bff-go-shopping-static-store/src/config/logger/formatters"
	"bff-go-shopping-static-store/src/config/logger/hooks"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"os"
	"time"

	//logrustash "github.com/bshuster-repo/logrus-logstash-hook"
	//formatters "github.com/fabienm/go-logrus-formatters"
	"github.com/labstack/echo/v4"
	"github.com/sirupsen/logrus"
)

var logg = logrus.New()
var logger *logrus.Entry
var appName = "bff-go-shopping-static-store"

func ConfigLogger() *logrus.Entry {
	logg.SetLevel(logrus.InfoLevel)
	logg.SetFormatter(&logrus.JSONFormatter{})

	hostname, _ := os.Hostname()
	conn, err := net.Dial("udp", os.Getenv("LOGSTASH_HOST"))
	if err != nil {
		logg.Fatal("Fail connect logstash: ", err)
	}
	hook := hooks.New(conn, formatters.NewGelf(hostname))
	logg.Hooks.Add(hook)

	logger = logg.WithFields(map[string]interface{}{
		"machine_name": hostname,
		"app_name":     appName,
		"tags":         os.Getenv("ENV") + ", " + appName,
		"app_version":  "1.0.0",
	})
	return logger
}

func GetLogger() *logrus.Entry {
	return logger
}

func Debug(args ...interface{}) {
	logger.Debug(args...)
}

func Info(args ...interface{}) {
	logger.Info(args...)
}

func InfoS(jsonStr string, args ...interface{}) {
	logger.WithFields(parseFields(jsonStr, args...)).Info("")
}

func InfoF(source string, shortMessage string, message string) {
	logger.WithFields(map[string]interface{}{
		"source":        source,
		"short_message": shortMessage,
		"message":       message,
	}).Info("")
}

func Error(args ...interface{}) {
	logger.Error(args...)
}

func ErrorS(err error, jsonStr string, args ...interface{}) {
	logger.WithFields(parseFields(jsonStr, args...)).Error(err)
}

func ErrorF(err error, source string, shortMessage string, message string) {
	logger.WithFields(map[string]interface{}{
		"source":        source,
		"short_message": shortMessage,
		"message":       message,
	}).Error(err)
}

func Fatal(args ...interface{}) {
	logger.Fatal(args...)
}

func FatalS(jsonStr string, args ...interface{}) {
	logger.WithFields(parseFields(jsonStr, args...)).Fatal("")
}

func ConfigContextLogger(ctx echo.Context) *logrus.Entry {
	rid := ctx.Get("request_id")
	transactionId := ctx.Get("transaction_id")
	contextLogger := logger.WithFields(map[string]interface{}{
		"request_id":     rid,
		"transaction_id": transactionId,
	})
	return contextLogger
}

func InfoWithContext(ctx context.Context, source string, shortMessage string, message string) {
	getLoggerFromContext(ctx).WithFields(map[string]interface{}{
		"source":        source,
		"short_message": shortMessage,
		"message":       message,
	}).Info("")
}

func ErrorWithContext(ctx context.Context, err error, source string, shortMessage string, message string) {
	if err == nil {
		err = errors.New(message)
	}
	getLoggerFromContext(ctx).WithFields(map[string]interface{}{
		"source":        source,
		"short_message": shortMessage,
		"message":       message,
		"error_message": err.Error(),
	}).Error(err)
}

func TimeElapsed(ctx context.Context, start time.Time, source string, shortMessage string, message string) {
	elapsed := time.Since(start)
	getLoggerFromContext(ctx).WithFields(map[string]interface{}{
		"source":        source,
		"duration":      int64(elapsed / time.Millisecond),
		"short_message": shortMessage,
		"message":       message,
	}).Info("")
}

func getLoggerFromContext(ctx context.Context) *logrus.Entry {
	return ctx.Value("logger").(*logrus.Entry)
}

func parseFields(jsonStr string, args ...interface{}) logrus.Fields {
	var fields map[string]interface{}
	json.Unmarshal([]byte(fmt.Sprintf(jsonStr, args...)), &fields)
	return fields
}
