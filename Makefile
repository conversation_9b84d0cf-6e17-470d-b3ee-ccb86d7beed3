BINARY=engine

engine:
	go build -o ${BINARY} main.go

setup:
	export GOPRIVATE=git.cvc.com.br
	export GOSUMDB=off
	export GONOSUMDB=git.cvc.com.br
	export GONOPROXY=git.cvc.com.br
	go clean -modcache
	go get -u ./...
	go mod vendor

build:
	go mod vendor
	go build -o bin/bff-go-shopping-static-store -mod=vendor

vendor:
	@go mod vendor

clean:
	go clean
	-find . -name ".out" -exec rm -f {} \;

docker:
	docker build -t bff-go-shopping-static-store .

run:
	docker-compose up --build -d

stop:
	docker-compose down

lint-prepare:
	@echo "Installing golangci-lint" 
	curl -sfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh| sh -s latest

lint:
	./bin/golangci-lint run ./...

.PHONY: clean install unittest build docker run stop vendor lint-prepare lint
