package util

import (
	"bff-go-shopping-static-store/src/entities"
	"encoding/base64"
	"encoding/json"
	"regexp"
	"strings"
	"unicode"

	"github.com/google/uuid"
	"golang.org/x/text/runes"
	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

func NewUUID() string {
	transactionId, uuidErr := uuid.NewUUID()
	if uuidErr != nil {
		return ""
	}
	return transactionId.String()
}

func NormalizeString(str string) string {
	t := transform.Chain(norm.NFD, runes.Remove(runes.In(unicode.Mn)), norm.NFC)
	strNorm, _, _ := transform.String(t, strings.ToLower(str))
	return strNorm
}

func NormalizeStringAZ09(str string) string {
	strNorm := NormalizeString(str)
	regex := regexp.MustCompile(`[^a-zA-Z0-9]`)
	strNorm = regex.ReplaceAllString(strNorm, "")
	return strNorm
}

func FindInStringArr(arr []string, search string) bool {
	for i := range arr {
		if strings.EqualFold(arr[i], search) {
			return true
		}
	}
	return false
}

func ParseAccessToken(accessToken string) (entities.Credential, error) {
	var credential entities.Credential

	arr := strings.Split(accessToken, ".")
	if len(arr) > 1 {
		rawDecodedText, _ := base64.RawURLEncoding.DecodeString(arr[1])

		// fmt.Println("DECODED: " + string(rawDecodedText))
		payload := entities.AccessTokenPayload{}
		json.Unmarshal([]byte(rawDecodedText), &payload)
		credential = payload.Credential
	}

	return credential, nil
}
