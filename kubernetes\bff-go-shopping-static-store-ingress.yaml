apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bff-go-shopping-static-store-ingress
  namespace: sub-fretamento
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/server-snippet: keepalive_timeout 300s; grpc_read_timeout 300s; grpc_send_timeout 300s;client_body_timeout 300s;
    nginx.org/ssl-backends: "bff-go-shopping-static-store-service"
spec:
  ingressClassName: "nginx-private"
  rules:
    - host: bff-go-shopping-static-store.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: bff-go-shopping-static-store-service
                port:
                  number: 8080
