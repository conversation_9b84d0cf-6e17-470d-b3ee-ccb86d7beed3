package request

type RequestSearchPackages struct {
	Origin               string                         `json:"origin"`
	Destination          string                         `json:"destination"`
	FromIata             string                         `json:"fromIata"`
	ToIata               string                         `json:"toIata"`
	StartCheckInDate     string                         `json:"startCheckInDate" example:"YYYY-MM-DD"`
	EndCheckInDate       string                         `json:"endCheckInDate" example:"YYYY-MM-DD"`
	Rooms                string                         `json:"rooms" example:"30,30"`
	SortBy               string                         `json:"sortBy" enums:"lower_price,higher_price"`
	Filters              RequestSearchPackagesFilter    `json:"filters"`
	Page                 int                            `json:"page"`
	Size                 int                            `json:"size"`
	Promotion            RequestSearchPackagesPromotion `json:"promotion"`
	OnlyBestPrice        *bool                          `json:"onlyBestPrice"`
	IncludeAirProtection *bool                          `json:"includeAirProtection"`
	ProductType          *string                        `json:"productType"`
}

type RequestSearchPackagesFilter struct {
	HotelName         string           `json:"hotelName"`
	Categories        []string         `json:"categories"`
	MealPlans         []string         `json:"mealPlans"`
	TotalPackageValue FilterInputRange `json:"totalPackageValue"`
}
type RequestSearchPackagesPromotion struct {
	Params        RequestPromoParams `json:"params"`
	SelectedItems []RequestPromoItem `json:"selectedItems"`
}
type RequestPromoItem struct {
	Rph       int    `json:"rph"`
	Group     string `json:"group"`
	RateToken string `json:"rateToken"`
}
type RequestPromoParams struct {
	Cpf          string   `json:"cpf"`
	PromoCode    []string `json:"promoCode"`
	ProfileName  string   `json:"profileName"`
	ShowSteps    bool     `json:"showSteps"`
	ShowPayloads bool     `json:"showPayloads"`
	ShowDetail   bool     `json:"showDetail"`
	Wait         int      `json:"wait"`
	Points       int      `json:"points"`
	Type         string   `json:"type"`
}
type RequestPromotion struct {
	AvailableItems []RequestPromoItem `json:"availableItems"`
	SelectedItems  []RequestPromoItem `json:"selectedItems"`
	Params         RequestPromoParams `json:"params"`
}
