@Library('cvc-<PERSON>en<PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "bff-go-shopping-static-store",
    "git": {
        "repositoryUrl": "******************:shopping/package/bff-go-shopping-static-store.git"
    },
    "technology": {
        "name": "GO",
        "version": "1.17.5",
        "buildCommands": {
            "buildApp": "go mod download -x && go build -v -o server main.go",
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "sub-fretamento"
    }
}
""")

final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {
                        
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
    post {
        always {
            dir("${this.env.WORKSPACE}") {
                //Clean Workspace
                deleteDir()
            }
        }
    }
}