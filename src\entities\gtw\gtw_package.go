package gtw

type GTWPackage struct {
	ID                string                 `json:"id"`
	Risk              RiskLevel              `json:"risk"`
	TourCode          string                 `json:"tourCode"`
	Title             string                 `json:"title"`
	Days              int                    `json:"days"`
	CheckIn           string                 `json:"checkIn"`
	CheckOut          string                 `json:"checkOut"`
	Products          []string               `json:"products"`
	Origin            GTWOrigin              `json:"origin"`
	Destination       GTWDestination         `json:"destination"`
	Hotels            []GTWHotel             `json:"hotels"`
	Flights           []GTWFlight            `json:"flights"`
	Links             GTWLinks               `json:"links"`
	PackageToken      string                 `json:"packageToken"`
	AirProtection     bool                   `json:"airProtection"`
	GroupReserve      bool                   `json:"groupReserve"`
	IsLandCombo       bool                   `json:"isLandCombo"`
	Dcr               *GTWDcr                `json:"dcr,omitempty"`
	IncludedServices  []GTWIncludedService   `json:"includedServices"`
	ItineraryDayByDay []GTWItineraryDayByDay `json:"itineraryDayByDay"`
	Suggested         *bool                  `json:"suggested"`
}

type GTWOrigin struct {
	Location GTWPackageLocation `json:"location"`
}

type GTWDestination struct {
	ID          string             `json:"id"`
	Description string             `json:"description"`
	Warning     string             `json:"warning"`
	Location    GTWPackageLocation `json:"location"`
	Tags        []GTWTags          `json:"tags"`
	Links       GTWLinks           `json:"links"`
}

type GTWPackageLocation struct {
	ID          string         `json:"id"`
	City        string         `json:"city"`
	State       string         `json:"state"`
	Country     string         `json:"country"`
	Coordinates GTWCoordinates `json:"coordinates"`
}

type GTWCoordinates struct {
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
}

type GTWTags struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type GTWIncludedService struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type GTWItineraryDayByDay struct {
	Label       string `json:"label"`
	Description string `json:"description"`
}

type RiskLevel string

const (
	Low    RiskLevel = "LOW"
	Medium RiskLevel = "MEDIUM"
	High   RiskLevel = "HIGH"
)

// return package value (first room)
func (p *GTWPackage) GetPackageValue() float64 {
	// Verifica se há um objeto Promotion associado
	if p.Hotels[0].Rooms[0].Rates[0].Promotion != nil && p.Hotels[0].Rooms[0].Rates[0].Promotion.Promotion != nil {
		// Verifica se o campo PriceWithTax dentro do objeto Promotion é válido
		if p.Hotels[0].Rooms[0].Rates[0].Promotion.Promotion.PriceWithTax > 0 {
			return p.Hotels[0].Rooms[0].Rates[0].Promotion.Promotion.PriceWithTax
		}
	}

	// Se não houver Promotion ou PriceWithTax inválido, retorna o valor padrão
	return p.Hotels[0].Rooms[0].Rates[0].PriceWithTax
}

// SortPkgByLowerPrice implements sort.Interface based on higher package value
type SortPkgByLowerPrice []GTWPackage

func (a SortPkgByLowerPrice) Len() int { return len(a) }
func (a SortPkgByLowerPrice) Less(i, j int) bool {
	return a[i].GetPackageValue() < a[j].GetPackageValue()
}
func (a SortPkgByLowerPrice) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

// SortPkgByLowerPrice implements sort.Interface based on lower package value
type SortPkgByHigherPrice []GTWPackage

func (a SortPkgByHigherPrice) Len() int { return len(a) }
func (a SortPkgByHigherPrice) Less(i, j int) bool {
	return a[i].GetPackageValue() > a[j].GetPackageValue()
}
func (a SortPkgByHigherPrice) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
