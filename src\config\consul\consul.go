package consul

import (
	"os"
	"time"

	"bff-go-shopping-static-store/src/config"
	"bff-go-shopping-static-store/src/config/logger"

	"github.com/spf13/viper"
	_ "github.com/spf13/viper/remote"
)

const CONSUL_SECONDS_RELOAD = 30

func ReadConfig() *config.BFFConfig {
	logger.InfoF("consul.ReadConfig", "start", "Starting StartConsul:")

	if err := viper.AddRemoteProvider("consul", os.<PERSON>env("CONSUL_HOST")+":"+os.<PERSON>env("CONSUL_PORT"), os.Getenv("CONSUL_KEY")); err != nil {
		logger.ErrorF(err, "consul.ReadConfig", "error", "Error adding Consul as a remote env provider: Details")
		panic(err)
	}

	viper.SetConfigType("json")
	if err := viper.ReadRemoteConfig(); err != nil {
		logger.ErrorF(err, "consul.ReadConfig", "error", "Error adding Consul as a remote env provider: Details")
		panic(err)
	}

	var conf *config.BFFConfig
	err := viper.UnmarshalKey("BFFConfig", &conf)
	if err != nil {
		logger.ErrorF(err, "consul.ReadConfig", "error", "error parsing to BFFConfig")
		panic(err)
	}

	go reloadConfig(conf)

	config.SetConfig(conf)
	return conf
}

func reloadConfig(conf *config.BFFConfig) {
	for {
		time.Sleep(time.Second * CONSUL_SECONDS_RELOAD)
		err := viper.WatchRemoteConfig()
		if err != nil {
			logger.ErrorF(err, "consul.reloadConfig", "error", "error reading consul")
			continue
		}

		err = viper.UnmarshalKey("BFFConfig", &conf)
		if err != nil {
			logger.ErrorF(err, "consul.reloadConfig", "error", "error parsing to BFFConfig")
		}
	}
}
