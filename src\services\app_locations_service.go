package services

import (
	"context"
	"sort"
	"strings"
	"time"

	"bff-go-shopping-static-store/src/config"
	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/entities"
	"bff-go-shopping-static-store/src/entities/gtw"
	"bff-go-shopping-static-store/src/util"
)

type APPLocationsService struct {
	config            *config.BFFConfig
	gtwPackageService GTWPackagesService
}

func NewAPPLocationsService(_gtwService *GTWPackagesService) *APPLocationsService {
	return &APPLocationsService{
		config:            config.GetConfig(),
		gtwPackageService: *_gtwService,
	}
}

func (s *APPLocationsService) SearchLocations(header entities.Header, searchText string, size int, isOrigin bool, isDestination bool, originId string, destinationId string, productType *string, includeAirProtection bool, ctx context.Context) ([]gtw.GTWLocation, error) {
	source := "APPLocationsService.SearchLocations"
	defer logger.TimeElapsed(ctx, time.Now(), source, "elapsed_time", "elapsed time")

	var resp []gtw.GTWLocation
	var err error

	resp, err = s.gtwPackageService.GetLocations(header, isOrigin, isDestination, originId, destinationId, size, productType, includeAirProtection, ctx)

	if err == nil {
		parseIata(resp)
		if searchText != "" {
			resp = filterByText(searchText, resp)
		}
		sortByName(resp)
	}

	return resp, err
}

func (s *APPLocationsService) SearchLocationsPackage(header entities.Header, active bool, iata string, charter bool, zoneId string, ctx context.Context) ([]gtw.GTWLocation, error) {

	source := "APPLocationsService.SearchLocationsPackage"
	defer logger.TimeElapsed(ctx, time.Now(), source, "elapsed_time", "elapsed time")

	var resp []gtw.GTWLocation
	var err error

	resp, err = s.gtwPackageService.GetLocationsPackage(header, active, iata, charter, zoneId, ctx)

	return resp, err
}

func parseIata(locations []gtw.GTWLocation) {
	for i := 0; i < len(locations); i++ {
		locations[i].IATA = ""
		if locations[i].LocationProducts != nil && len(locations[i].LocationProducts) > 0 {
			locationProduct := locations[i].LocationProducts[0]
			if locationProduct.IataList != nil && len(locationProduct.IataList) > 0 {
				locations[i].IATA = locationProduct.IataList[0].Iata
			}
		}
	}
}

func filterByText(searchText string, locations []gtw.GTWLocation) []gtw.GTWLocation {
	result := []gtw.GTWLocation{}
	sText := util.NormalizeString(searchText)
	for _, location := range locations {
		if strings.EqualFold(sText, location.IATA) ||
			strings.Contains(util.NormalizeString(location.City.Name), sText) ||
			strings.Contains(util.NormalizeString(location.Name), sText) {
			result = append(result, location)
		}
	}
	return result
}

func sortByName(locations []gtw.GTWLocation) {
	sort.Sort(gtw.SortLocationByName(locations))
}
