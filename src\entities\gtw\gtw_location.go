package gtw

import "bff-go-shopping-static-store/src/util"

type GTWLocationWrapper struct {
	Content []GTWLocation `json:"content"`
}

type GTWLocation struct {
	ID               string                `json:"id"`
	Name             string                `json:"name"`
	Description      string                `json:"description"`
	City             GTWLocationCity       `json:"city"`
	IATA             string                `json:"iata"`
	LocationProducts []GTWLocationProducts `json:"locationProducts"`
}

type GTWLocationCity struct {
	ID    string           `json:"id"`
	Name  string           `json:"name"`
	State GTWLocationState `json:"state"`
}

type GTWLocationState struct {
	ID        string             `json:"id"`
	Name      string             `json:"name"`
	ShortName string             `json:"shortName"`
	Country   GTWLocationCountry `json:"country"`
}

type GTWLocationCountry struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	ShortName string `json:"shortName"`
}

type GTWLocationProducts struct {
	IataList []GTWIataList `json:"iataList"`
}

type GTWIataList struct {
	Iata        string `json:"iata"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// SortLocationByName implements sort.Interface based on location name
type SortLocationByName []GTWLocation

func (a SortLocationByName) Len() int { return len(a) }
func (a SortLocationByName) Less(i, j int) bool {
	return util.NormalizeString(a[i].Name) < util.NormalizeString(a[j].Name)
}
func (a SortLocationByName) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
