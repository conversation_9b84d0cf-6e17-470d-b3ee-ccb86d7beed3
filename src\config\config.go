package config

type BFFConfig struct {
	GTWAccessTokenDefault  string
	GTWPackages            string
	GTWPackagesLocations   string
	GTWPackagesSearch      string
	GTWPackagesCalendar    string
	GTWPackagesProductType string
	GTWPackagesHasAvail    string
	UseGTWPromotion        bool
	GTWPromotion           string
	GTWPromotionTimeout    int
	GTWPromotionRetries    int
}

var bffConfig *BFFConfig

func SetConfig(_config *BFFConfig) {
	bffConfig = _config
}

func GetConfig() *BFFConfig {
	return bffConfig
}
