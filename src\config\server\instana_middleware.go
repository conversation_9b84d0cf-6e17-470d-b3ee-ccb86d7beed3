package server

import (
	"net/http"
	"strings"

	instana "github.com/instana/go-sensor"
	"github.com/labstack/echo/v4"
)

func InstanaMiddleware(sensor *instana.Sensor) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {

			//skip swagger
			if strings.Contains(c.Path(), "/swagger/") {
				return next(c)
			}

			r := lookupMatchedRoute(c)
			if r == nil {
				r = &echo.Route{
					Method: c.Request().Method,
					Path:   c.Path(),
				}
			}

			var err error
			instana.TracingNamedHandlerFunc(sensor, r.Name, r.Path, func(w http.ResponseWriter, req *http.Request) {
				c.SetResponse(echo.NewResponse(w, c.Echo()))
				c.SetRequest(req)
				if err = next(c); err != nil {
					c.Error(err)
				}
			})(c.Response(), c.Request())
			return err
		}
	}
}

func lookupMatchedRoute(c echo.Context) *echo.Route {
	path := c.Path()
	for _, r := range c.Echo().Routes() {
		if r.Path == path {
			return r
		}
	}
	return nil
}
