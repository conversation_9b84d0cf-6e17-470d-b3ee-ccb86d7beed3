package util

import "strings"

var dictAmenities map[string]string

func GetDictAmenities() map[string]string {
	if dictAmenities == nil {
		dictAmenities = make(map[string]string)
		dictAmenities[strings.ToLower("Banho turco")] = "Hidromassagem"
		dictAmenities[strings.ToLower("Business center")] = "Business Center"
		dictAmenities[strings.ToLower("Piscina al aire libre")] = "Piscina"
		dictAmenities[strings.ToLower("Área para piqueniques")] = "Equipe de Recreação"
		dictAmenities[strings.ToLower("Lavandaria/limpeza a seco")] = "Lavanderia"
		dictAmenities[strings.ToLower("Traslado de/para o aeroporto (sobretaxa)")] = "Transfer"
		dictAmenities[strings.ToLower("Ar-condictAmenitiesionado central  1")] = "Ar CondictAmenitiesionado"
		dictAmenities[strings.ToLower("Transporte para o terminal de cruzeiros (sobretaxa)")] = "Transfer"
		dictAmenities[strings.ToLower("Quartos para deficientes")] = "Comodidades para pessoas com mobilidade reduzida"
		dictAmenities[strings.ToLower("Serviço de lavandaria")] = "Lavanderia"
		dictAmenities[strings.ToLower("Gymnasium ")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Desayuno en la habitación")] = "Lanchonete"
		dictAmenities[strings.ToLower("Apto. para portadores de deficiência")] = "Comodidades para pessoas com mobilidade reduzida"
		dictAmenities[strings.ToLower("Playground aquático ")] = "Kids Club"
		dictAmenities[strings.ToLower("Cable tv")] = "Tv a cabo"
		dictAmenities[strings.ToLower("Piscina para crianças")] = "Piscina"
		dictAmenities[strings.ToLower("DIÁRIA(S) COM MEIA PENSÃO")] = "Meia Pensão"
		dictAmenities[strings.ToLower("Sem Café da Manhã")] = "Sem Café"
		dictAmenities[strings.ToLower("DIÁRIA(S) COM CAFÉ DA MANHÃ CONTINENTAL")] = "Café da Manhã"
		dictAmenities[strings.ToLower("Telefone com correio de voz")] = "Telefone"
		dictAmenities[strings.ToLower("Jacuzzi")] = "Spa"
		dictAmenities[strings.ToLower("Sala de fitness")] = "Academia"
		dictAmenities[strings.ToLower("Mercearia/loja de conveniência")] = "Lojas"
		dictAmenities[strings.ToLower("Registo de entrada rápido")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("Bar")] = "Bar"
		dictAmenities[strings.ToLower("Cabeleireiro")] = "Salão de beleza"
		dictAmenities[strings.ToLower("Serviço de babysitting")] = "Baby Sitter"
		dictAmenities[strings.ToLower("Café da Manhã")] = "Café da Manhã"
		dictAmenities[strings.ToLower("Desayuno disponible")] = "Lanchonete"
		dictAmenities[strings.ToLower("Mesa de bilhar")] = "Sala de Jogos"
		dictAmenities[strings.ToLower("Zona de refeições ")] = "Restaurante"
		dictAmenities[strings.ToLower("Pensão Completa")] = "Pensão Completa"
		dictAmenities[strings.ToLower("Chaleira")] = "Cafeteira e/ou chaleira"
		dictAmenities[strings.ToLower("Serviços de cocktails")] = "Bar"
		dictAmenities[strings.ToLower("Parquinho")] = "Kids Club"
		dictAmenities[strings.ToLower("Multibanco")] = "Lojas"
		dictAmenities[strings.ToLower("Ténis no local")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Restaurante buffet")] = "Restaurante"
		dictAmenities[strings.ToLower("Lojas/shopping")] = "Lojas"
		dictAmenities[strings.ToLower("Banheira de hidromassagem")] = "Hidromassagem"
		dictAmenities[strings.ToLower("Gimnasio")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Agência de câmbios")] = "Balcão de Turismo"
		dictAmenities[strings.ToLower("Mini golfe")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Televisão")] = "TV"
		dictAmenities[strings.ToLower("Air conditioning")] = "Ar CondictAmenitiesionado"
		dictAmenities[strings.ToLower("Garagem")] = "Estacionamento"
		dictAmenities[strings.ToLower("Tudo Incluso")] = "All Inclusive"
		dictAmenities[strings.ToLower("Phone")] = "Telefone"
		dictAmenities[strings.ToLower("Sala de jogos")] = "Sala de Jogos"
		dictAmenities[strings.ToLower("Centro de conferências")] = "Business Center"
		dictAmenities[strings.ToLower("DIARIA(S) COM CAFÉ / NÃO COBRA RESORT FEE")] = "Café da Manhã"
		dictAmenities[strings.ToLower("DIÁRIA(S) COM CAFÉ DA MANHÃ BUFFET")] = "Café da Manhã"
		dictAmenities[strings.ToLower("Café/chá nos espaços comuns")] = "Cafeteira e/ou chaleira"
		dictAmenities[strings.ToLower("Parque aquático ")] = "Piscina"
		dictAmenities[strings.ToLower("Tennis courts")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Esportes aquáticos")] = "Piscina"
		dictAmenities[strings.ToLower("Varanda com rede")] = "Terraço"
		dictAmenities[strings.ToLower("DIÁRIAS SEM CAFÉ DA MANHÃ")] = "Sem Café"
		dictAmenities[strings.ToLower("Loja de lembranças")] = "Lojas"
		dictAmenities[strings.ToLower("Campo de ténis exterior")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Tv")] = "TV"
		dictAmenities[strings.ToLower("Máquina de café")] = "Coffee Shop"
		dictAmenities[strings.ToLower("Internet Banda Larga")] = "Wi-fi"
		dictAmenities[strings.ToLower("Aluguer de automóveis")] = "Lojas"
		dictAmenities[strings.ToLower("Pequeno-almoço grátis ")] = "Restaurante"
		dictAmenities[strings.ToLower("Assistência para passeios/bilhetes")] = "Balcão de Turismo"
		dictAmenities[strings.ToLower("Supermercado")] = "Lojas"
		dictAmenities[strings.ToLower("Snack bar/deli")] = "Bar"
		dictAmenities[strings.ToLower("Casa de banho acessível para cadeiras de rodas")] = "Comodidades para pessoas com mobilidade reduzida"
		dictAmenities[strings.ToLower("DIÁRIA(S) ALL INCLUSIVE")] = "All Inclusive"
		dictAmenities[strings.ToLower("DIÁRIA(S) COM CAFÉ DA MANHÃ")] = "Café da Manhã"
		dictAmenities[strings.ToLower("Serviço de limpeza diário")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Telefone ddd e ddi")] = "Telefone"
		dictAmenities[strings.ToLower("Terraço/solário ")] = "Terraço"
		dictAmenities[strings.ToLower("Berço disponivel a pedido")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Conserje")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Clube infantil")] = "Kids Club"
		dictAmenities[strings.ToLower("Terraço ")] = "Terraço"
		dictAmenities[strings.ToLower("Piscina exterior")] = "Piscina"
		dictAmenities[strings.ToLower("Ar-condictAmenitiesionado quente e frio ")] = "Ar CondictAmenitiesionado"
		dictAmenities[strings.ToLower("Ar condictAmenitiesionado em zonas comuns")] = "Ar CondictAmenitiesionado"
		dictAmenities[strings.ToLower("Transporte para o casino grátis")] = "Transfer"
		dictAmenities[strings.ToLower("Frigobar")] = "Frigobar"
		dictAmenities[strings.ToLower("Pizzaria")] = "Restaurante"
		dictAmenities[strings.ToLower("Babá")] = "Baby Sitter"
		dictAmenities[strings.ToLower("Salas de reuniões")] = "Business Center"
		dictAmenities[strings.ToLower("Transporte para o terminal marítimo (sobretaxa)")] = "Transfer"
		dictAmenities[strings.ToLower("Flat-panel tv ")] = "TV"
		dictAmenities[strings.ToLower("Ducha com aquecimento solar")] = "Banheiro Privativo"
		dictAmenities[strings.ToLower("Salão de Beleza")] = "Salão de beleza"
		dictAmenities[strings.ToLower("Escorrega aquático")] = "Piscina"
		dictAmenities[strings.ToLower("Estacionamento")] = "Estacionamento"
		dictAmenities[strings.ToLower("Pequeno-almoço buffet")] = "Restaurante"
		dictAmenities[strings.ToLower("Equipa poliglota")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("Banheiro acessível")] = "Comodidades para pessoas com mobilidade reduzida"
		dictAmenities[strings.ToLower("Bagageiro")] = "Depósito de bagagem"
		dictAmenities[strings.ToLower("Bar de piscina")] = "Bar"
		dictAmenities[strings.ToLower("Caixa atm/banco")] = "Lojas"
		dictAmenities[strings.ToLower("Mini-clube")] = "Kids Club"
		dictAmenities[strings.ToLower("Disabled facilities")] = "Comodidades para pessoas com mobilidade reduzida"
		dictAmenities[strings.ToLower("Desayunador")] = "Lanchonete"
		dictAmenities[strings.ToLower("Safe deposit box")] = "Cofre"
		dictAmenities[strings.ToLower("DIÁRIA(S) SEM CAFÉ DA MANHÃ")] = "Sem Café"
		dictAmenities[strings.ToLower("Serviços/sala de bagagem")] = "Depósito de bagagem"
		dictAmenities[strings.ToLower("Serviço de massagens")] = "Spa"
		dictAmenities[strings.ToLower("Sala de reuniões")] = "Business Center"
		dictAmenities[strings.ToLower("Mordomo")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Wifi - areas publicas ")] = "Wi-fi nas áreas comuns"
		dictAmenities[strings.ToLower("Varanda")] = "Terraço"
		dictAmenities[strings.ToLower("Hora de check-in ")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("Ar-condictAmenitiesionado split")] = "Ar CondictAmenitiesionado"
		dictAmenities[strings.ToLower("Banheiro privativo com banho aquecido")] = "Banheiro Privativo"
		dictAmenities[strings.ToLower("Transporte para o aeroporto (sobretaxa)")] = "Transfer"
		dictAmenities[strings.ToLower("Clube para crianças (grátis)")] = "Kids Club"
		dictAmenities[strings.ToLower("Hora de check-out")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("Car parking (payable to hotel, if applicable)")] = "Estacionamento"
		dictAmenities[strings.ToLower("Cambios")] = "Lojas"
		dictAmenities[strings.ToLower("Apoio para excursões/compra de bilhetes")] = "Balcão de Turismo"
		dictAmenities[strings.ToLower("Babá a pedido")] = "Baby Sitter"
		dictAmenities[strings.ToLower("Máquina de secar roupa ")] = "Lavanderia"
		dictAmenities[strings.ToLower("Complimentary wireless internet")] = "Wi-fi"
		dictAmenities[strings.ToLower("Serviço de baby-sitter ou guarda de crianças")] = "Baby Sitter"
		dictAmenities[strings.ToLower("Massagens a pedido")] = "Spa"
		dictAmenities[strings.ToLower("Banheiro com ducha")] = "Banheiro Privativo"
		dictAmenities[strings.ToLower("Telefone")] = "Telefone"
		dictAmenities[strings.ToLower("Centro de congressos")] = "Business Center"
		dictAmenities[strings.ToLower("Accessibilidade para cadeira de rodas")] = "Comodidades para pessoas com mobilidade reduzida"
		dictAmenities[strings.ToLower("Piscina")] = "Piscina"
		dictAmenities[strings.ToLower("Posto de turismo")] = "Balcão de Turismo"
		dictAmenities[strings.ToLower("Bar/lounge")] = "Bar"
		dictAmenities[strings.ToLower("Tábua e ferro de passar")] = "Ferro de passar roupa"
		dictAmenities[strings.ToLower("Serviços de concierge")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Clube infantil (sobretaxa)")] = "Kids Club"
		dictAmenities[strings.ToLower("Ténis ")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Piscina externa infantil de água salgada")] = "Piscina"
		dictAmenities[strings.ToLower("Cofre para laptop")] = "Cofre"
		dictAmenities[strings.ToLower("Lojas de presentes ou banca de jornal ")] = "Lojas"
		dictAmenities[strings.ToLower("Refrigerator")] = "Frigobar"
		dictAmenities[strings.ToLower("Ponto para internet banda larga")] = "Wi-fi nas áreas comuns"
		dictAmenities[strings.ToLower("Ar-condictAmenitiesionado")] = "Ar CondictAmenitiesionado"
		dictAmenities[strings.ToLower("Free wifi")] = "Wi-fi nas áreas comuns"
		dictAmenities[strings.ToLower("Recepção 24 h por dia")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("24-hour room service")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Sala de tv")] = "TV"
		dictAmenities[strings.ToLower("Ascensor")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Minibar")] = "Bar"
		dictAmenities[strings.ToLower("Meia Pensão")] = "Meia Pensão"
		dictAmenities[strings.ToLower("Uma sala de reuniões")] = "Business Center"
		dictAmenities[strings.ToLower("Lojas no local ")] = "Lojas"
		dictAmenities[strings.ToLower("Pequeno-almoço disponível (sobretaxa)")] = "Restaurante"
		dictAmenities[strings.ToLower("Vigilância 24 h por dia ")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("Restaurante publico")] = "Restaurante"
		dictAmenities[strings.ToLower("Registo de saída rápido")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("Tv por assinatura")] = "Tv a cabo"
		dictAmenities[strings.ToLower("Tennis ")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Chamadas locais e internacionais")] = "Telefone"
		dictAmenities[strings.ToLower("Amenities de banho")] = "Banheiro Privativo"
		dictAmenities[strings.ToLower("Children''s pool")] = "Piscina"
		dictAmenities[strings.ToLower("Mesa de bilhar ou de snooker")] = "Sala de Jogos"
		dictAmenities[strings.ToLower("Valet same day dry cleaning ")] = "Lavanderia"
		dictAmenities[strings.ToLower("Aventura seagway")] = "Equipe de Recreação"
		dictAmenities[strings.ToLower("Discoteca")] = "Discoteca"
		dictAmenities[strings.ToLower("Transfer do aeroporto  ")] = "Transfer"
		dictAmenities[strings.ToLower("DIARIA(S) SEM CAFÉ / NÃO COBRA RESORT FEE")] = "Sem Café"
		dictAmenities[strings.ToLower("Chuveiro com barras de apoio")] = "Comodidades para pessoas com mobilidade reduzida"
		dictAmenities[strings.ToLower("Depósito de bagagens")] = "Depósito de bagagem"
		dictAmenities[strings.ToLower("Quadra de padel")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Jardim")] = "Jardim"
		dictAmenities[strings.ToLower("Garagem paga")] = "Estacionamento"
		dictAmenities[strings.ToLower("Kids club")] = "Kids Club"
		dictAmenities[strings.ToLower("Caixa de segurança eletrônica")] = "Lojas"
		dictAmenities[strings.ToLower("Beauty parlour")] = "Salão de beleza"
		dictAmenities[strings.ToLower("Piscina interior")] = "Piscina"
		dictAmenities[strings.ToLower("Bar/salon")] = "Bar"
		dictAmenities[strings.ToLower("Cofre")] = "Cofre"
		dictAmenities[strings.ToLower("Academia de ginástica gratuita")] = "Academia"
		dictAmenities[strings.ToLower("Auditório")] = "Business Center"
		dictAmenities[strings.ToLower("Spa")] = "Spa"
		dictAmenities[strings.ToLower("Serviço de quarto 24 horas")] = "Serviço de quarto"
		dictAmenities[strings.ToLower("Restaurante privado")] = "Restaurante"
		dictAmenities[strings.ToLower("Tv lcd a cabo")] = "Tv a cabo"
		dictAmenities[strings.ToLower("Restaurante")] = "Restaurante"
		dictAmenities[strings.ToLower("Bengaleiro")] = "Lojas"
		dictAmenities[strings.ToLower("Atividades supervisionadas/guarda de crianças (grátis)")] = "Equipe de Recreação"
		dictAmenities[strings.ToLower("Estacionamento de ônibus")] = "Estacionamento"
		dictAmenities[strings.ToLower("DIÁRIA(S) COM PENSÃO COMPLETA")] = "Pensão Completa"
		dictAmenities[strings.ToLower("Estacionamento gratuito")] = "Estacionamento"
		dictAmenities[strings.ToLower("Ginásio")] = "Quadra poliesportiva"
		dictAmenities[strings.ToLower("Sala de jogos eletrónicos/de mesa")] = "Sala de Jogos"
		dictAmenities[strings.ToLower("Cabeleireiro a pedido")] = "Salão de beleza"
		dictAmenities[strings.ToLower("Recepção 24 horas")] = "Recepção 24 horas"
		dictAmenities[strings.ToLower("Secretária eletrônica - tel")] = "Telefone"
		dictAmenities[strings.ToLower("Cafetaria")] = "Coffee Shop"
		dictAmenities[strings.ToLower("Serviço de baby-sitter ou supervisão de crianças (sobretaxa)")] = "Baby Sitter"
		dictAmenities[strings.ToLower("Ar-condictAmenitiesionado individual ")] = "Ar CondictAmenitiesionado"
		dictAmenities[strings.ToLower("Equipa multilingue")] = "Recepção 24 horas"
	}
	return dictAmenities
}
