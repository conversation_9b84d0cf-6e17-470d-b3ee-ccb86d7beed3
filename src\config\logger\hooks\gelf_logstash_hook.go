// "github.com/bshuster-repo/logrus-logstash-hook"
package hooks

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"sync"

	"github.com/sirupsen/logrus"
)

// Hook represents a Logstash hook.
type Hook struct {
	writer    io.Writer
	formatter logrus.Formatter
}

// New returns a new logrus.Hook for Logstash.
func New(w io.Writer, f logrus.Formatter) logrus.Hook {
	return Hook{
		writer:    w,
		formatter: f,
	}
}

// 1k bytes buffer by default
var bufPool = sync.Pool{
	New: func() interface{} {
		return bytes.NewBuffer(make([]byte, 0, 1024))
	},
}

func newBuffer() *bytes.Buffer {
	b := bufPool.Get().(*bytes.Buffer)
	if b != nil {
		b.Reset()
		return b
	}
	return bytes.NewBuffer(nil)
}

func (h Hook) Fire(e *logrus.Entry) error {
	dataBytes, err := h.formatter.Format(e)
	if err != nil {
		return err
	}

	//use gzip to compress message
	var (
		zBuf   *bytes.Buffer
		zBytes []byte
	)
	var zw io.WriteCloser
	zBuf = newBuffer()
	defer bufPool.Put(zBuf)
	zw, err = gzip.NewWriterLevel(zBuf, 6)
	if err != nil {
		fmt.Println("Fail gzip write: ", err)
		return err
	}
	if _, err = zw.Write(dataBytes); err != nil {
		zw.Close()
		return err
	}
	zw.Close()
	zBytes = zBuf.Bytes()

	n, err := h.writer.Write(zBytes)
	if n != len(zBytes) {
		return fmt.Errorf("bad write (%d/%d)", n, len(zBytes))
	}

	return err
}

// Levels returns all logrus levels.
func (h Hook) Levels() []logrus.Level {
	return logrus.AllLevels
}

// Using a pool to re-use of old entries when formatting Logstash messages.
// It is used in the Fire function.
var entryPool = sync.Pool{
	New: func() interface{} {
		return &logrus.Entry{}
	},
}
