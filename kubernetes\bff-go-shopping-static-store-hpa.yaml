apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: bff-go-shopping-static-store-hpa
  namespace: sub-fretamento
  labels:
    app: bff-go-shopping-static-store
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bff-go-shopping-static-store-deploy
  minReplicas: 5
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 500
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 150
