package services

import (
	"bff-go-shopping-static-store/src/config"
	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/entities"
	"bff-go-shopping-static-store/src/entities/gtw"
	"bff-go-shopping-static-store/src/entities/request"
	"bff-go-shopping-static-store/src/util"
	"context"
	"math"
	"sort"
	"strings"
	"time"
)

type APPPackagesService struct {
	config              *config.BFFConfig
	gtwPackageService   GTWPackagesService
	gtwPromotionService GTWPromotionService
}

// public functions
func NewAPPPackagesService(_gtwService *GTWPackagesService, _gtwPromotionService *GTWPromotionService) *APPPackagesService {
	return &APPPackagesService{
		config:              config.GetConfig(),
		gtwPackageService:   *_gtwService,
		gtwPromotionService: *_gtwPromotionService,
	}
}

func (s *APPPackagesService) SearchPackages(header entities.Header, request *request.RequestSearchPackages, ctx context.Context) (gtw.GTWPackagesResponse, error) {
	startTime := time.Now()
	source := "APPPackagesService.SearchPackages"

	var response gtw.GTWPackagesResponse
	var err error
	if (request.Origin != "" && request.Destination != "") || (request.FromIata != "" && request.ToIata != "") {
		response, err = s.gtwPackageService.GetPackages(header, request.Origin, request.Destination, request.FromIata, request.ToIata, request.StartCheckInDate, request.EndCheckInDate, request.Rooms, ctx, request.OnlyBestPrice, request.IncludeAirProtection, request.ProductType)
	} else {
		response, err = s.gtwPackageService.GetPackagesByLocation(header, request.Origin, request.Destination, request.StartCheckInDate, request.EndCheckInDate, request.Rooms, request.ProductType, ctx)
	}

	if err == nil && len(response.Packages) > 0 {
		//parse, filter, sort and paginate
		parsePackagesResponse(&response)
		filterPackagesResponse(request, &response)
		sortPackagesResponse(request, &response)
		paginatePackagesResponse(request.Size, request.Page, &response)

		//request promotions
		if s.config.UseGTWPromotion {
			responsePromotion := s.requestPackagePromotions(header, request.Promotion, &response, ctx)
			response.Metrics.Promotions = responsePromotion.Metrics
			sortPackagesResponse(request, &response)
		}
	}

	response.Metrics.Result.Duration = int(time.Since(startTime) / time.Millisecond)
	response.Metrics.Result.StatusCode = 200

	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response, err
}

func (s *APPPackagesService) SearchAlternativePackages(header entities.Header, origin string, destination string, startCheckInDate string, endCheckInDate string, rooms string, productType *string, includeAirProtection bool, ctx context.Context) ([]gtw.GTWPackage, error) {
	startTime := time.Now()
	source := "APPPackagesService.SearchAlternativePackages"

	response, err := s.gtwPackageService.GetAlternativePackages(header, origin, destination, startCheckInDate, endCheckInDate, rooms, productType, includeAirProtection, ctx)

	if err == nil {
		parsePackagesResponse(&response)
		sortByLowerPrice(&response)
		filterPackagesByUniqueLocations(&response)
	}

	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response.Packages, err
}

func (s *APPPackagesService) GetPackageByToken(header entities.Header, packageToken string, ctx context.Context) (gtw.GTWPackage, error) {
	response, err := s.gtwPackageService.GetPackageByToken(header, packageToken, ctx)
	return response, err
}

func (s *APPPackagesService) GetDCR(header entities.Header, packageToken string, ctx context.Context) (gtw.GTWDcr, error) {
	response, err := s.gtwPackageService.GetDCR(header, packageToken, ctx)
	return response, err
}

func (s *APPPackagesService) GetCalendar(header entities.Header, origin string, destination string, startCheckInDate string, numberOfMonths int, rooms string, productType *string, includeAirProtection bool, ctx context.Context) (gtw.GTWCalendarWrapper, error) {
	response, err := s.gtwPackageService.GetCalendar(header, origin, destination, startCheckInDate, numberOfMonths, rooms, productType, includeAirProtection, ctx)
	return response, err
}

func (s *APPPackagesService) AdvancedSearch(header entities.Header, request *request.RequestSearchPackages, paxes string, departureDate string, flightsGroupUserId string, reservationCode string, tourCode string, packageId string, ctx context.Context) ([]gtw.GTWPackage, *gtw.GTWRestError) {
	startTime := time.Now()
	source := "APPPackagesService.AdvancedSearch"

	response, err := s.gtwPackageService.GetAdvancedSearch(header, paxes, departureDate, flightsGroupUserId, reservationCode, tourCode, packageId, ctx)

	if err == nil {
		parsePackagesResponse(&response)
		sortByLowerPrice(&response)
		// filterPackagesByUniqueLocations(&response)

		//request promotions
		if s.config.UseGTWPromotion {
			responsePromotion := s.requestPackagePromotions(header, request.Promotion, &response, ctx)
			response.Metrics.Promotions = responsePromotion.Metrics
		}
	}

	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response.Packages, err
}

func (s *APPPackagesService) HasAvailByRateToken(header entities.Header, rateToken string, ctx context.Context) (gtw.GTWPackageHasAvail, *gtw.GTWRestError) {
	response, err := s.gtwPackageService.HasAvailByRateToken(header, rateToken, ctx)
	return response, err
}

// private functions
func (s *APPPackagesService) requestPackagePromotions(header entities.Header, searchPromoRequest request.RequestSearchPackagesPromotion, response *gtw.GTWPackagesResponse, ctx context.Context) gtw.GTWPromotion {
	var requestPromoItems []request.RequestPromoItem
	rph := 0
	for _, pkg := range response.Packages {
		for _, room := range pkg.Hotels[0].Rooms {
			availableItem := request.RequestPromoItem{
				Rph:       rph,
				Group:     "1",
				RateToken: room.Rates[0].RateToken,
			}
			requestPromoItems = append(requestPromoItems, availableItem)
			rph++
		}
	}
	requestPromotion := request.RequestPromotion{}
	requestPromotion.SelectedItems = []request.RequestPromoItem{}
	if searchPromoRequest.SelectedItems != nil {
		requestPromotion.SelectedItems = searchPromoRequest.SelectedItems
	}
	requestPromotion.AvailableItems = requestPromoItems

	promotionResponse, err := s.gtwPromotionService.RequestPromotions(header, requestPromotion, ctx)
	if err == nil && promotionResponse.AvailableItems != nil {
		rph = 0
		for pi := 0; pi < len(response.Packages); pi++ {
			for ri := 0; ri < len(response.Packages[pi].Hotels[0].Rooms); ri++ {
				promotionItem := promotionResponse.AvailableItems[rph]
				response.Packages[pi].Hotels[0].Rooms[ri].Rates[0].Promotion = &promotionItem
				rph++
			}
		}
	}
	return promotionResponse
}

// parse functions
func parsePackagesResponse(response *gtw.GTWPackagesResponse) {
	for i := 0; i < len(response.Packages); i++ {
		if response.Packages[i].Hotels != nil && len(response.Packages[i].Hotels) > 0 {
			//parse amenities name
			for hi, hotel := range response.Packages[i].Hotels {
				for ai, amenities := range hotel.Amenities {
					value, exist := util.GetDictAmenities()[strings.ToLower(amenities.Name)]
					if exist {
						response.Packages[i].Hotels[hi].Amenities[ai].Name = value
					}
					response.Packages[i].Hotels[hi].Amenities[ai].Code = util.NormalizeStringAZ09(response.Packages[i].Hotels[hi].Amenities[ai].Name)
				}
			}
		}

		//generate flight id
		if response.Packages[i].Flights != nil && len(response.Packages[i].Flights) > 0 {
			response.Packages[i].IsLandCombo = false
			for fi, flight := range response.Packages[i].Flights {
				var flightId = ""
				for _, segment := range flight.Segments {
					for _, leg := range segment.Legs {
						flightId += leg.Departure + "_" + leg.DepartureDate + "_" + leg.Arrival + "_" + leg.ArrivalDate + "_" + leg.FlightCode + "_" + leg.FlightNumber + "_"
					}
				}
				response.Packages[i].Flights[fi].Id = flightId
			}
		} else {
			response.Packages[i].IsLandCombo = true
		}
	}
}

func paginatePackagesResponse(size int, page int, response *gtw.GTWPackagesResponse) {
	totalPackages := len(response.Packages)
	if size <= 0 {
		size = totalPackages
	}
	totalPages := int(math.Ceil(float64(totalPackages) / float64(size)))
	if page <= 0 || page > totalPages {
		page = 1
	}
	start := (page - 1) * size
	limit := start + size
	if limit > totalPackages {
		limit = totalPackages
	}

	response.Packages = response.Packages[start:limit]
	response.Meta.TotalPackages = totalPackages
	response.Meta.TotalPages = totalPages
}

// filter functions
func filterPackagesResponse(request *request.RequestSearchPackages, response *gtw.GTWPackagesResponse) {
	result := []gtw.GTWPackage{}

	for _, pkg := range response.Packages {
		isValidHotel := true

		//filter by hotel name
		if isValidHotel && request.Filters.HotelName != "" {
			isValidHotel = filterByHotelName(pkg.Hotels[0], request.Filters.HotelName)
		}

		if isValidHotel {
			filteredRooms := []gtw.GTWRooms{}
			for _, room := range pkg.Hotels[0].Rooms {
				isRoomValid := true

				//filter by value
				if isRoomValid && request.Filters.TotalPackageValue.Max > 0 {
					isRoomValid = filterByRoomValue(room, request.Filters.TotalPackageValue.Min, request.Filters.TotalPackageValue.Max)
				}
				//filter by category
				if isRoomValid && len(request.Filters.Categories) > 0 {
					isRoomValid = filterByCategory(room, request.Filters.Categories)
				}
				//filter by mealplan
				if isRoomValid && len(request.Filters.MealPlans) > 0 {
					isRoomValid = filterByMealPlan(room, request.Filters.MealPlans)
				}
				if isRoomValid {
					filteredRooms = append(filteredRooms, room)
				}
			}
			pkg.Hotels[0].Rooms = filteredRooms

			//append only hotels with valid rooms
			if len(pkg.Hotels[0].Rooms) > 0 {
				result = append(result, pkg)
			}
		}
	}

	response.Packages = result
}

func filterByHotelName(hotel gtw.GTWHotel, text string) bool {
	return strings.Contains(util.NormalizeString(hotel.Name), util.NormalizeString(text))
}

func filterByRoomValue(room gtw.GTWRooms, min float64, max float64) bool {
	return room.GetRoomValue() >= min && room.GetRoomValue() <= max
}

func filterByCategory(room gtw.GTWRooms, categories []string) bool {
	return util.FindInStringArr(categories, room.Category)
}

func filterByMealPlan(room gtw.GTWRooms, mealPlans []string) bool {
	return util.FindInStringArr(mealPlans, room.MealPlan)
}

func filterPackagesByUniqueLocations(response *gtw.GTWPackagesResponse) {
	result := []gtw.GTWPackage{}
	for _, pkg := range response.Packages {
		exists := false
		for _, pkgResult := range result {
			if strings.EqualFold(pkgResult.Destination.Location.ID, pkg.Destination.Location.ID) {
				exists = true
				break
			}
		}
		if !exists {
			result = append(result, pkg)
		}
	}
	response.Packages = result
}

// sort functions
func sortPackagesResponse(request *request.RequestSearchPackages, response *gtw.GTWPackagesResponse) {
	switch request.SortBy {
	case "lower_price":
		sortByLowerPrice(response)
	case "higher_price":
		sortByHigherPrice(response)
	case "preferential":
		// Mantem a ordem original
	default:
		// Mantem a ordem original
	}
}

func sortByLowerPrice(response *gtw.GTWPackagesResponse) {
	//sort rooms first
	for _, item := range response.Packages {
		sort.Sort(gtw.SortRoomByLowerPrice(item.Hotels[0].Rooms))
	}
	sort.Sort(gtw.SortPkgByLowerPrice(response.Packages))
}

func sortByHigherPrice(response *gtw.GTWPackagesResponse) {
	//sort rooms first
	for _, item := range response.Packages {
		sort.Sort(gtw.SortRoomByHigherPrice(item.Hotels[0].Rooms))
	}
	sort.Sort(gtw.SortPkgByHigherPrice(response.Packages))
}
