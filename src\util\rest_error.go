package util

import (
	"bff-go-shopping-static-store/src/entities"
	"net/http"
)

func NewBadRequestError(message string) entities.ResponseError {
	return entities.ResponseError{
		StatusCode: http.StatusBadRequest,
		Message:    message,
		Code:       "bad_request",
	}
}

func NewGenericError(message string) entities.ResponseError {
	return entities.ResponseError{
		StatusCode: http.StatusInternalServerError,
		Message:    message,
		Code:       "ERR",
	}
}

func NewNotFoundError(message string) entities.ResponseError {
	return entities.ResponseError{
		StatusCode: http.StatusNotFound,
		Message:    message,
		Code:       "404",
	}
}
