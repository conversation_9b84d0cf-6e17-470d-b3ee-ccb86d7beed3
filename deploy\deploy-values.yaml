environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 2Gi
                cpu: 2000m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 5
            maxReplicas: 20
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 150
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-go-shopping-static-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: PREFIX
                value: bff-go-shopping-static-store
              - name: VAULT_HOST
                value: vault-dev.services.cvc.com.br
              - name: CONSUL_PORT
                value: "8500"
              - name: LOGSTASH_HOST
                value: logstash-qa.services.cvc.com.br:12201
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: ENV
                value: ti
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: CONSUL_KEY
                value: platform/brand-contexts/packages-charter
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 2Gi
                cpu: 2000m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 5
            maxReplicas: 20
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 150
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-go-shopping-static-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: ENV
                value: qa
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: PREFIX
                value: bff-go-shopping-static-store
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_KEY
                value: platform/brand-contexts/packages-charter
              - name: LOGSTASH_HOST
                value: logstash-qa.services.cvc.com.br:12201
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 2Gi
                cpu: 2000m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 5
            maxReplicas: 20
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 150
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-go-shopping-static-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: ENV
                value: qa
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: PREFIX
                value: bff-go-shopping-static-store
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_KEY
                value: platform/brand-contexts/packages-charter
              - name: LOGSTASH_HOST
                value: logstash-qa.services.cvc.com.br:12201
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 2Gi
                cpu: 2000m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 5
            maxReplicas: 20
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 150
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-go-shopping-static-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: CONSUL_KEY
                value: platform/brand-contexts/packages-charter
              - name: LOGSTASH_HOST
                value: logstash.services.cvc.com.br:12201
              - name: ENV
                value: prod
              - name: PREFIX
                value: bff-go-shopping-static-store
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 2Gi
                cpu: 2000m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 5
            maxReplicas: 20
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 150
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-go-shopping-static-store
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: CONSUL_KEY
                value: platform/brand-contexts/packages-charter
              - name: LOGSTASH_HOST
                value: logstash.services.cvc.com.br:12201
              - name: ENV
                value: prod
              - name: PREFIX
                value: bff-go-shopping-static-store
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
