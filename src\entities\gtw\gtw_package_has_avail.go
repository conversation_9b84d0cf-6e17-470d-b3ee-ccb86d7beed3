package gtw

type GTWPackageHasAvail struct {
	PackageRateToken string          `json:"packageRateToken"`
	PackagePrice     GTWPackagePrice `json:"packagePrice"`
	GroupReserve     bool            `json:"groupReserve"`
}

type GTWPackagePrice struct {
	PackageGroup          string  `json:"packageGroup,omitempty"`
	Currency              string  `json:"currency,omitempty"`
	PriceWithTax          float64 `json:"priceWithTax,omitempty"`
	PriceWithoutTax       float64 `json:"priceWithoutTax,omitempty"`
	PricePerDayWithTax    float64 `json:"pricePerDayWithTax,omitempty"`
	PricePerDayWithoutTax float64 `json:"pricePerDayWithoutTax,omitempty"`
	PricePerPaxWithTax    float64 `json:"pricePerPaxWithTax,omitempty"`
	PricePerPaxWithoutTax float64 `json:"pricePerPaxWithoutTax,omitempty"`
}
