package gtw

type GTWFlight struct {
	Id           string          `json:"id"`
	Risk         RiskLevel       `json:"risk"`
	ValidatingBy GTWValidatingBy `json:"validatingBy"`
	FareGroup    GTWFareGroup    `json:"fareGroup"`
	Segments     []GTWSegments   `json:"segments"`
	Airports     []GTWAirports   `json:"airports"`
}

type GTWValidatingBy struct {
	Name string `json:"name"`
	Iata string `json:"iata"`
}

type GTWFareGroup struct {
	Currency              string     `json:"currency"`
	PlayerPriceWithTax    float64    `json:"playerPriceWithTax"`
	PlayerPriceWithoutTax float64    `json:"playerPriceWithoutTax"`
	PriceWithTax          float64    `json:"priceWithTax"`
	PriceWithoutTax       float64    `json:"priceWithoutTax"`
	Fares                 []GTWFares `json:"fares"`
}

type GTWBaggage struct {
	IsIncluded  bool    `json:"isIncluded"`
	Type        string  `json:"type"`
	Quantity    float64 `json:"quantity"`
	Description string  `json:"description"`
}

type GTWServices struct {
	Type        string `json:"type"`
	IsIncluded  bool   `json:"isIncluded"`
	Description string `json:"description"`
}

type GTWFareProfile struct {
	Baggages []GTWBaggage  `json:"baggages"`
	Services []GTWServices `json:"services"`
}

type GTWSeatClass struct {
	Code        string `json:"code"`
	Description string `json:"description"`
}

type GTWLegs struct {
	Status        string       `json:"status"`
	FlightCode    string       `json:"flightCode"`
	FlightNumber  string       `json:"flightNumber"`
	Duration      int          `json:"duration"`
	Departure     string       `json:"departure"`
	Arrival       string       `json:"arrival"`
	DepartureDate string       `json:"departureDate"`
	ArrivalDate   string       `json:"arrivalDate"`
	AircraftCode  string       `json:"aircraftCode"`
	NumberOfStops int          `json:"numberOfStops"`
	SeatClass     GTWSeatClass `json:"seatClass"`
	FareBasis     string       `json:"fareBasis"`
	FareClass     string       `json:"fareClass"`
}

type GTWSegments struct {
	Departure     string         `json:"departure"`
	DepartureDate string         `json:"departureDate"`
	Arrival       string         `json:"arrival"`
	ArrivalDate   string         `json:"arrivalDate"`
	RateToken     string         `json:"rateToken"`
	RouteRPH      int            `json:"routeRPH"`
	Rph           int            `json:"rph"`
	PackageGroup  string         `json:"packageGroup"`
	NumberOfStops int            `json:"numberOfStops"`
	FareType      string         `json:"fareType"`
	Duration      int            `json:"duration"`
	FareProfile   GTWFareProfile `json:"fareProfile"`
	Legs          []GTWLegs      `json:"legs"`
}

type GTWAirportLocation struct {
	Name string `json:"name"`
}

type GTWAirports struct {
	Name     string             `json:"name"`
	Iata     string             `json:"iata"`
	Stop     bool               `json:"stop"`
	Location GTWAirportLocation `json:"location"`
}

type GTWFares struct {
	PassengersType  string           `json:"passengersType"`
	PassengersCount int              `json:"passengersCount"`
	PriceWithTax    float64          `json:"priceWithTax"`
	PriceWithoutTax float64          `json:"priceWithoutTax"`
	Taxes           []GTWFlightTaxes `json:"taxes"`
}

type GTWFlightTaxes struct {
	Code        string                `json:"code"`
	Description string                `json:"description"`
	Amount      float64               `json:"amount"`
	Currency    string                `json:"currency"`
	Values      []GTWFlightTaxesValue `json:"values"`
}

type GTWFlightTaxesValue struct {
	Currency string  `json:"currency"`
	Amount   float64 `json:"amount"`
}
