package services

import (
	"context"
	"errors"
	"fmt"
	"net/http/httputil"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"

	"bff-go-shopping-static-store/src/config"
	"bff-go-shopping-static-store/src/config/logger"
	"bff-go-shopping-static-store/src/entities"
	"bff-go-shopping-static-store/src/entities/gtw"
	"bff-go-shopping-static-store/src/entities/request"
)

type GTWPromotionService struct {
	config *config.BFFConfig
}

func NewGTWPromotionService() *GTWPromotionService {
	return &GTWPromotionService{
		config: config.GetConfig(),
	}
}

func (r *GTWPromotionService) RequestPromotions(header entities.Header, requestPromotion request.RequestPromotion, ctx context.Context) (gtw.GTWPromotion, error) {
	startTime := time.Now()
	source := "GTWPromotionService.RequestPromotions"
	uri := r.config.GTWPromotion

	if requestPromotion.Params.Type == "" {
		requestPromotion.Params.Type = "CLUB"
	}
	requestPromotion.Params.ProfileName = "pac-avail"
	requestPromotion.Params.Wait = (r.config.GTWPromotionTimeout * 1000)
	requestPromotion.Params.ShowDetail = false
	requestPromotion.Params.ShowPayloads = false
	requestPromotion.Params.ShowSteps = false

	logger.InfoWithContext(ctx, source, "start", "REQUEST: "+uri)
	// // print request json
	// bodyRequest, _ := json.Marshal(requestPromotion)
	// fmt.Println("----- REQUEST ------")
	// fmt.Println(string(bodyRequest))
	// fmt.Println("----- -------- ------")

	var response gtw.GTWPromotion
	httpResponse, err := r.getRestClient(header, ctx).
		SetBody(requestPromotion).
		SetResult(&response).
		Post(uri)

	// r.printCurlCommand(httpResponse)

	metric := entities.MetricsResult{
		StatusCode: httpResponse.StatusCode(),
		Duration:   int(time.Since(startTime) / time.Millisecond),
	}
	response.Metrics = append([]entities.MetricsResult{}, metric)

	if err != nil {
		logger.ErrorWithContext(ctx, err, source, "error", "error executing api promotion")
		return response, errors.New("error executing api promotion")
	}
	if httpResponse.StatusCode() != 200 {
		logger.InfoWithContext(ctx, source, "error", "api promotion returns status: "+httpResponse.Status())
		return response, errors.New("api promotion returns status: " + httpResponse.Status())
	}

	logger.InfoWithContext(ctx, source, "success", "Success")
	logger.TimeElapsed(ctx, startTime, source, "elapsed_time", "elapsed time")
	return response, nil
}

func (r *GTWPromotionService) getRestClient(header entities.Header, ctx context.Context) *resty.Request {
	accessToken := header.AccessToken
	if accessToken == "" {
		accessToken = r.config.GTWAccessTokenDefault
	}

	client := resty.New()
	client.
		SetTimeout(time.Duration(r.config.GTWPromotionTimeout) * time.Second).
		SetRetryCount(r.config.GTWPromotionRetries - 1).
		SetRetryWaitTime(1 * time.Second).
		AddRetryCondition(
			func(r *resty.Response, err error) bool {
				if err != nil {
					logger.ErrorWithContext(ctx, err, "GTWPromotionService.RetryCondition", "error", "error executing api "+r.Request.URL)
					return true
				}
				if r.IsError() {
					logger.InfoWithContext(ctx, "GTWPromotionService.RetryCondition", "error", "api "+r.Request.URL+" returns status: "+r.RawResponse.Status)
					return true
				}
				return false
			},
		).
		AddRetryHook(
			func(r *resty.Response, err error) {
				logger.InfoWithContext(ctx, "GTWPromotionService.RetryCondition", "attempt "+strconv.Itoa(r.Request.Attempt), "fail executing api "+r.Request.URL)
			},
		)

	request := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("gtw-transaction-id", header.TransactionId)
	if accessToken != "" {
		request = request.SetHeader("gtw-sec-user-token", accessToken)
	}
	return request
}

func (*GTWPromotionService) printCurlCommand(httpResponse *resty.Response) {
	req := httpResponse.Request.RawRequest

	curlCommand, err := httputil.DumpRequestOut(req, true)
	if err != nil {
		fmt.Println("Erro ao criar comando cURL:", err)
		return
	}
	_ = len(curlCommand)

	//Descomentar para printar o cURL
	// fmt.Println("CURL ------------------------------------------------------------------------------------")
	// fmt.Println(string(curlCommand))
	// fmt.Println("CURL FINAL --- Convert http to cUrl [https://curl.se/h2c/] -------------------------------")
}